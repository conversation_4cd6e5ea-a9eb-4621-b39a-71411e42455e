[{"E:\\Uroom\\Admin\\src\\index.js": "1", "E:\\Uroom\\Admin\\src\\App.js": "2", "E:\\Uroom\\Admin\\src\\reportWebVitals.js": "3", "E:\\Uroom\\Admin\\src\\redux\\store.js": "4", "E:\\Uroom\\Admin\\src\\utils\\Routes.js": "5", "E:\\Uroom\\Admin\\src\\redux\\socket\\socketSlice.js": "6", "E:\\Uroom\\Admin\\src\\redux\\root-reducer.js": "7", "E:\\Uroom\\Admin\\src\\redux\\root-saga.js": "8", "E:\\Uroom\\Admin\\src\\pages\\BannedPage.jsx": "9", "E:\\Uroom\\Admin\\src\\pages\\DashboardAdmin.jsx": "10", "E:\\Uroom\\Admin\\src\\pages\\feedback\\ListFeedbackAdminPage.jsx": "11", "E:\\Uroom\\Admin\\src\\pages\\customer\\DetailCustomerAdmin.jsx": "12", "E:\\Uroom\\Admin\\src\\pages\\customer\\ListCustomerAdmin.jsx": "13", "E:\\Uroom\\Admin\\src\\pages\\reported_feedback\\DetailReportedAdmin.jsx": "14", "E:\\Uroom\\Admin\\src\\pages\\reported_feedback\\ReportedFeedbackAdmin.jsx": "15", "E:\\Uroom\\Admin\\src\\pages\\hotelHost\\DetailHotelHostAdmin.jsx": "16", "E:\\Uroom\\Admin\\src\\pages\\hotelHost\\ApprovalAccountHotelhost.jsx": "17", "E:\\Uroom\\Admin\\src\\pages\\hotelHost\\HotelManagement.jsx": "18", "E:\\Uroom\\Admin\\src\\pages\\payment\\ListPaymentHotel.jsx": "19", "E:\\Uroom\\Admin\\src\\pages\\hotelHost\\TransactionHotelhost.jsx": "20", "E:\\Uroom\\Admin\\src\\pages\\login_register\\RegisterPage.jsx": "21", "E:\\Uroom\\Admin\\src\\pages\\login_register\\VerifyCodePage.jsx": "22", "E:\\Uroom\\Admin\\src\\pages\\login_register\\LoginPage.jsx": "23", "E:\\Uroom\\Admin\\src\\pages\\login_register\\ForgetPasswordPage.jsx": "24", "E:\\Uroom\\Admin\\src\\pages\\login_register\\VerifyCodeRegisterPage.jsx": "25", "E:\\Uroom\\Admin\\src\\pages\\login_register\\ResetPasswordPage.jsx": "26", "E:\\Uroom\\Admin\\src\\redux\\feedback\\saga.js": "27", "E:\\Uroom\\Admin\\src\\redux\\reportedFeedback\\saga.js": "28", "E:\\Uroom\\Admin\\src\\redux\\auth\\saga.js": "29", "E:\\Uroom\\Admin\\src\\redux\\auth\\reducer.js": "30", "E:\\Uroom\\Admin\\src\\redux\\auth\\actions.js": "31", "E:\\Uroom\\Admin\\src\\redux\\reportedFeedback\\reducer.js": "32", "E:\\Uroom\\Admin\\src\\utils\\handleToken.js": "33", "E:\\Uroom\\Admin\\src\\redux\\feedback\\actions.js": "34", "E:\\Uroom\\Admin\\src\\redux\\feedback\\reducer.js": "35", "E:\\Uroom\\Admin\\src\\pages\\payment\\ListPaymentCustomer.jsx": "36", "E:\\Uroom\\Admin\\src\\redux\\reportedFeedback\\actions.js": "37", "E:\\Uroom\\Admin\\src\\redux\\promotion\\reducer.js": "38", "E:\\Uroom\\Admin\\src\\redux\\message\\reducer.js": "39", "E:\\Uroom\\Admin\\src\\redux\\message\\saga.js": "40", "E:\\Uroom\\Admin\\src\\redux\\adminDashboard\\saga.js": "41", "E:\\Uroom\\Admin\\src\\redux\\adminDashboard\\reducer.js": "42", "E:\\Uroom\\Admin\\src\\redux\\promotion\\saga.js": "43", "E:\\Uroom\\Admin\\src\\pages\\SidebarAdmin.jsx": "44", "E:\\Uroom\\Admin\\src\\redux\\auth\\factories.js": "45", "E:\\Uroom\\Admin\\src\\components\\ToastContainer.jsx": "46", "E:\\Uroom\\Admin\\src\\adapter\\ApiConstants.js": "47", "E:\\Uroom\\Admin\\src\\components\\ConfirmationModal.jsx": "48", "E:\\Uroom\\Admin\\src\\pages\\approve\\ApprovePage.jsx": "49", "E:\\Uroom\\Admin\\src\\pages\\dashboard\\DashboardPage.jsx": "50", "E:\\Uroom\\Admin\\src\\pages\\messenger\\Chat.jsx": "51", "E:\\Uroom\\Admin\\src\\pages\\promotion\\ListPromotionPage.jsx": "52", "E:\\Uroom\\Admin\\src\\utils\\Utils.js": "53", "E:\\Uroom\\Admin\\src\\pages\\login_register\\GoogleLogin.jsx": "54", "E:\\Uroom\\Admin\\src\\libs\\api\\index.js": "55", "E:\\Uroom\\Admin\\src\\redux\\feedback\\factories.js": "56", "E:\\Uroom\\Admin\\src\\redux\\reportedFeedback\\factories.js": "57", "E:\\Uroom\\Admin\\src\\redux\\message\\actions.js": "58", "E:\\Uroom\\Admin\\src\\redux\\adminDashboard\\actions.js": "59", "E:\\Uroom\\Admin\\src\\redux\\adminDashboard\\factories.js": "60", "E:\\Uroom\\Admin\\src\\redux\\message\\factories.js": "61", "E:\\Uroom\\Admin\\src\\redux\\promotion\\factories.js": "62", "E:\\Uroom\\Admin\\src\\utils\\fonts.js": "63", "E:\\Uroom\\Admin\\src\\redux\\promotion\\actions.js": "64", "E:\\Uroom\\Admin\\src\\pages\\promotion\\DetailPromotionPage.jsx": "65", "E:\\Uroom\\Admin\\src\\libs\\firebaseConfig.js": "66"}, {"size": 839, "mtime": 1753036043087, "results": "67", "hashOfConfig": "68"}, {"size": 4119, "mtime": 1753036043054, "results": "69", "hashOfConfig": "68"}, {"size": 375, "mtime": 1753036043106, "results": "70", "hashOfConfig": "68"}, {"size": 1291, "mtime": 1753036043106, "results": "71", "hashOfConfig": "68"}, {"size": 1302, "mtime": 1753036043107, "results": "72", "hashOfConfig": "68"}, {"size": 1451, "mtime": 1753036043106, "results": "73", "hashOfConfig": "68"}, {"size": 733, "mtime": 1753036043105, "results": "74", "hashOfConfig": "68"}, {"size": 568, "mtime": 1753036043106, "results": "75", "hashOfConfig": "68"}, {"size": 1474, "mtime": 1753036043088, "results": "76", "hashOfConfig": "68"}, {"size": 16320, "mtime": 1753036043089, "results": "77", "hashOfConfig": "68"}, {"size": 10290, "mtime": 1753036043092, "results": "78", "hashOfConfig": "68"}, {"size": 14538, "mtime": 1753036043090, "results": "79", "hashOfConfig": "68"}, {"size": 22581, "mtime": 1753036043090, "results": "80", "hashOfConfig": "68"}, {"size": 15326, "mtime": 1753036043097, "results": "81", "hashOfConfig": "68"}, {"size": 6292, "mtime": 1753036043098, "results": "82", "hashOfConfig": "68"}, {"size": 25537, "mtime": 1753036043092, "results": "83", "hashOfConfig": "68"}, {"size": 1723, "mtime": 1753036043092, "results": "84", "hashOfConfig": "68"}, {"size": 18937, "mtime": 1753036043093, "results": "85", "hashOfConfig": "68"}, {"size": 24809, "mtime": 1753036043096, "results": "86", "hashOfConfig": "68"}, {"size": 3050, "mtime": 1753036043093, "results": "87", "hashOfConfig": "68"}, {"size": 7403, "mtime": 1753036043094, "results": "88", "hashOfConfig": "68"}, {"size": 7520, "mtime": 1753036043094, "results": "89", "hashOfConfig": "68"}, {"size": 15641, "mtime": 1753036043094, "results": "90", "hashOfConfig": "68"}, {"size": 3718, "mtime": 1753036043093, "results": "91", "hashOfConfig": "68"}, {"size": 8490, "mtime": 1753036043095, "results": "92", "hashOfConfig": "68"}, {"size": 5423, "mtime": 1753036043094, "results": "93", "hashOfConfig": "68"}, {"size": 1225, "mtime": 1753036043100, "results": "94", "hashOfConfig": "68"}, {"size": 2761, "mtime": 1753036043105, "results": "95", "hashOfConfig": "68"}, {"size": 11749, "mtime": 1753036043099, "results": "96", "hashOfConfig": "68"}, {"size": 2374, "mtime": 1753036043099, "results": "97", "hashOfConfig": "68"}, {"size": 1424, "mtime": 1753036043099, "results": "98", "hashOfConfig": "68"}, {"size": 955, "mtime": 1753036043105, "results": "99", "hashOfConfig": "68"}, {"size": 551, "mtime": 1753036043107, "results": "100", "hashOfConfig": "68"}, {"size": 196, "mtime": 1753036043099, "results": "101", "hashOfConfig": "68"}, {"size": 764, "mtime": 1753036043100, "results": "102", "hashOfConfig": "68"}, {"size": 24271, "mtime": 1753036043096, "results": "103", "hashOfConfig": "68"}, {"size": 466, "mtime": 1753036043105, "results": "104", "hashOfConfig": "68"}, {"size": 5605, "mtime": 1753036043104, "results": "105", "hashOfConfig": "68"}, {"size": 551, "mtime": 1753036043100, "results": "106", "hashOfConfig": "68"}, {"size": 2085, "mtime": 1753036043100, "results": "107", "hashOfConfig": "68"}, {"size": 1477, "mtime": 1753036043098, "results": "108", "hashOfConfig": "68"}, {"size": 1558, "mtime": 1753036043098, "results": "109", "hashOfConfig": "68"}, {"size": 7219, "mtime": 1753036043105, "results": "110", "hashOfConfig": "68"}, {"size": 2422, "mtime": 1753036043089, "results": "111", "hashOfConfig": "68"}, {"size": 1617, "mtime": 1753036043099, "results": "112", "hashOfConfig": "68"}, {"size": 1493, "mtime": 1753036043055, "results": "113", "hashOfConfig": "68"}, {"size": 3791, "mtime": 1753036043055, "results": "114", "hashOfConfig": "68"}, {"size": 2348, "mtime": 1753036043055, "results": "115", "hashOfConfig": "68"}, {"size": 6543, "mtime": 1753036043089, "results": "116", "hashOfConfig": "68"}, {"size": 52967, "mtime": 1753036043091, "results": "117", "hashOfConfig": "68"}, {"size": 25958, "mtime": 1753036043095, "results": "118", "hashOfConfig": "68"}, {"size": 31702, "mtime": 1753036043097, "results": "119", "hashOfConfig": "68"}, {"size": 3227, "mtime": 1753036043107, "results": "120", "hashOfConfig": "68"}, {"size": 2140, "mtime": 1753036043093, "results": "121", "hashOfConfig": "68"}, {"size": 2658, "mtime": 1753036043087, "results": "122", "hashOfConfig": "68"}, {"size": 342, "mtime": 1753036043100, "results": "123", "hashOfConfig": "68"}, {"size": 595, "mtime": 1753036043105, "results": "124", "hashOfConfig": "68"}, {"size": 322, "mtime": 1753036043100, "results": "125", "hashOfConfig": "68"}, {"size": 887, "mtime": 1753036043098, "results": "126", "hashOfConfig": "68"}, {"size": 283, "mtime": 1753036043098, "results": "127", "hashOfConfig": "68"}, {"size": 377, "mtime": 1753036043100, "results": "128", "hashOfConfig": "68"}, {"size": 1732, "mtime": 1753036043104, "results": "129", "hashOfConfig": "68"}, {"size": 636, "mtime": 1753036043107, "results": "130", "hashOfConfig": "68"}, {"size": 4304, "mtime": 1753036043104, "results": "131", "hashOfConfig": "68"}, {"size": 18835, "mtime": 1753044895932, "results": "132", "hashOfConfig": "68"}, {"size": 693, "mtime": 1753036043088, "results": "133", "hashOfConfig": "68"}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1tmwm7w", {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\Uroom\\Admin\\src\\index.js", [], [], "E:\\Uroom\\Admin\\src\\App.js", ["332"], [], "E:\\Uroom\\Admin\\src\\reportWebVitals.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\store.js", [], [], "E:\\Uroom\\Admin\\src\\utils\\Routes.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\socket\\socketSlice.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\root-reducer.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\root-saga.js", [], [], "E:\\Uroom\\Admin\\src\\pages\\BannedPage.jsx", ["333"], [], "E:\\Uroom\\Admin\\src\\pages\\DashboardAdmin.jsx", ["334", "335", "336", "337", "338", "339", "340", "341", "342", "343", "344", "345", "346", "347", "348", "349", "350", "351"], [], "E:\\Uroom\\Admin\\src\\pages\\feedback\\ListFeedbackAdminPage.jsx", ["352", "353", "354", "355"], [], "E:\\Uroom\\Admin\\src\\pages\\customer\\DetailCustomerAdmin.jsx", ["356", "357", "358", "359"], [], "E:\\Uroom\\Admin\\src\\pages\\customer\\ListCustomerAdmin.jsx", ["360", "361", "362", "363", "364"], [], "E:\\Uroom\\Admin\\src\\pages\\reported_feedback\\DetailReportedAdmin.jsx", ["365", "366"], [], "E:\\Uroom\\Admin\\src\\pages\\reported_feedback\\ReportedFeedbackAdmin.jsx", ["367", "368", "369"], [], "E:\\Uroom\\Admin\\src\\pages\\hotelHost\\DetailHotelHostAdmin.jsx", ["370", "371", "372", "373"], [], "E:\\Uroom\\Admin\\src\\pages\\hotelHost\\ApprovalAccountHotelhost.jsx", ["374", "375"], [], "E:\\Uroom\\Admin\\src\\pages\\hotelHost\\HotelManagement.jsx", ["376", "377", "378", "379"], [], "E:\\Uroom\\Admin\\src\\pages\\payment\\ListPaymentHotel.jsx", ["380", "381", "382", "383"], [], "E:\\Uroom\\Admin\\src\\pages\\hotelHost\\TransactionHotelhost.jsx", ["384", "385"], [], "E:\\Uroom\\Admin\\src\\pages\\login_register\\RegisterPage.jsx", ["386", "387"], [], "E:\\Uroom\\Admin\\src\\pages\\login_register\\VerifyCodePage.jsx", ["388", "389", "390", "391", "392"], [], "E:\\Uroom\\Admin\\src\\pages\\login_register\\LoginPage.jsx", ["393"], [], "E:\\Uroom\\Admin\\src\\pages\\login_register\\ForgetPasswordPage.jsx", ["394", "395", "396", "397", "398"], [], "E:\\Uroom\\Admin\\src\\pages\\login_register\\VerifyCodeRegisterPage.jsx", [], [], "E:\\Uroom\\Admin\\src\\pages\\login_register\\ResetPasswordPage.jsx", ["399", "400"], [], "E:\\Uroom\\Admin\\src\\redux\\feedback\\saga.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\reportedFeedback\\saga.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\auth\\saga.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\auth\\reducer.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\auth\\actions.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\reportedFeedback\\reducer.js", [], [], "E:\\Uroom\\Admin\\src\\utils\\handleToken.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\feedback\\actions.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\feedback\\reducer.js", [], [], "E:\\Uroom\\Admin\\src\\pages\\payment\\ListPaymentCustomer.jsx", ["401", "402"], [], "E:\\Uroom\\Admin\\src\\redux\\reportedFeedback\\actions.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\promotion\\reducer.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\message\\reducer.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\message\\saga.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\adminDashboard\\saga.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\adminDashboard\\reducer.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\promotion\\saga.js", [], [], "E:\\Uroom\\Admin\\src\\pages\\SidebarAdmin.jsx", ["403", "404", "405", "406", "407", "408"], [], "E:\\Uroom\\Admin\\src\\redux\\auth\\factories.js", [], [], "E:\\Uroom\\Admin\\src\\components\\ToastContainer.jsx", [], [], "E:\\Uroom\\Admin\\src\\adapter\\ApiConstants.js", ["409", "410", "411"], [], "E:\\Uroom\\Admin\\src\\components\\ConfirmationModal.jsx", [], [], "E:\\Uroom\\Admin\\src\\pages\\approve\\ApprovePage.jsx", ["412", "413", "414", "415", "416"], [], "E:\\Uroom\\Admin\\src\\pages\\dashboard\\DashboardPage.jsx", ["417"], [], "E:\\Uroom\\Admin\\src\\pages\\messenger\\Chat.jsx", ["418", "419", "420", "421", "422", "423", "424", "425"], [], "E:\\Uroom\\Admin\\src\\pages\\promotion\\ListPromotionPage.jsx", ["426", "427"], [], "E:\\Uroom\\Admin\\src\\utils\\Utils.js", [], [], "E:\\Uroom\\Admin\\src\\pages\\login_register\\GoogleLogin.jsx", [], [], "E:\\Uroom\\Admin\\src\\libs\\api\\index.js", ["428"], [], "E:\\Uroom\\Admin\\src\\redux\\feedback\\factories.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\reportedFeedback\\factories.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\message\\actions.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\adminDashboard\\actions.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\adminDashboard\\factories.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\message\\factories.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\promotion\\factories.js", [], [], "E:\\Uroom\\Admin\\src\\utils\\fonts.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\promotion\\actions.js", [], [], "E:\\Uroom\\Admin\\src\\pages\\promotion\\DetailPromotionPage.jsx", [], [], "E:\\Uroom\\Admin\\src\\libs\\firebaseConfig.js", [], [], {"ruleId": "429", "severity": 1, "message": "430", "line": 40, "column": 6, "nodeType": "431", "endLine": 40, "endColumn": 17, "suggestions": "432"}, {"ruleId": "433", "severity": 1, "message": "434", "line": 1, "column": 17, "nodeType": "435", "messageId": "436", "endLine": 1, "endColumn": 26}, {"ruleId": "433", "severity": 1, "message": "437", "line": 4, "column": 10, "nodeType": "435", "messageId": "436", "endLine": 4, "endColumn": 14}, {"ruleId": "433", "severity": 1, "message": "438", "line": 4, "column": 16, "nodeType": "435", "messageId": "436", "endLine": 4, "endColumn": 19}, {"ruleId": "433", "severity": 1, "message": "439", "line": 4, "column": 21, "nodeType": "435", "messageId": "436", "endLine": 4, "endColumn": 24}, {"ruleId": "433", "severity": 1, "message": "440", "line": 4, "column": 26, "nodeType": "435", "messageId": "436", "endLine": 4, "endColumn": 34}, {"ruleId": "433", "severity": 1, "message": "441", "line": 21, "column": 8, "nodeType": "435", "messageId": "436", "endLine": 21, "endColumn": 25}, {"ruleId": "433", "severity": 1, "message": "442", "line": 28, "column": 8, "nodeType": "435", "messageId": "436", "endLine": 28, "endColumn": 29}, {"ruleId": "433", "severity": 1, "message": "443", "line": 66, "column": 28, "nodeType": "435", "messageId": "436", "endLine": 66, "endColumn": 47}, {"ruleId": "433", "severity": 1, "message": "444", "line": 68, "column": 25, "nodeType": "435", "messageId": "436", "endLine": 68, "endColumn": 41}, {"ruleId": "445", "severity": 1, "message": "446", "line": 179, "column": 19, "nodeType": "447", "endLine": 179, "endColumn": 76}, {"ruleId": "445", "severity": 1, "message": "446", "line": 189, "column": 19, "nodeType": "447", "endLine": 189, "endColumn": 78}, {"ruleId": "445", "severity": 1, "message": "446", "line": 199, "column": 19, "nodeType": "447", "endLine": 199, "endColumn": 76}, {"ruleId": "445", "severity": 1, "message": "446", "line": 209, "column": 19, "nodeType": "447", "endLine": 209, "endColumn": 77}, {"ruleId": "445", "severity": 1, "message": "446", "line": 219, "column": 19, "nodeType": "447", "endLine": 219, "endColumn": 76}, {"ruleId": "445", "severity": 1, "message": "446", "line": 234, "column": 19, "nodeType": "447", "endLine": 234, "endColumn": 75}, {"ruleId": "445", "severity": 1, "message": "446", "line": 244, "column": 19, "nodeType": "447", "endLine": 244, "endColumn": 84}, {"ruleId": "445", "severity": 1, "message": "446", "line": 254, "column": 19, "nodeType": "447", "endLine": 254, "endColumn": 74}, {"ruleId": "445", "severity": 1, "message": "446", "line": 269, "column": 19, "nodeType": "447", "endLine": 269, "endColumn": 76}, {"ruleId": "445", "severity": 1, "message": "448", "line": 377, "column": 25, "nodeType": "447", "endLine": 392, "endColumn": 26}, {"ruleId": "433", "severity": 1, "message": "449", "line": 9, "column": 3, "nodeType": "435", "messageId": "436", "endLine": 9, "endColumn": 13}, {"ruleId": "433", "severity": 1, "message": "450", "line": 18, "column": 13, "nodeType": "435", "messageId": "436", "endLine": 18, "endColumn": 20}, {"ruleId": "433", "severity": 1, "message": "451", "line": 23, "column": 9, "nodeType": "435", "messageId": "436", "endLine": 23, "endColumn": 17}, {"ruleId": "433", "severity": 1, "message": "452", "line": 24, "column": 19, "nodeType": "435", "messageId": "436", "endLine": 24, "endColumn": 29}, {"ruleId": "433", "severity": 1, "message": "434", "line": 1, "column": 20, "nodeType": "435", "messageId": "436", "endLine": 1, "endColumn": 29}, {"ruleId": "433", "severity": 1, "message": "453", "line": 16, "column": 10, "nodeType": "435", "messageId": "436", "endLine": 16, "endColumn": 16}, {"ruleId": "433", "severity": 1, "message": "454", "line": 41, "column": 10, "nodeType": "435", "messageId": "436", "endLine": 41, "endColumn": 25}, {"ruleId": "433", "severity": 1, "message": "455", "line": 50, "column": 9, "nodeType": "435", "messageId": "436", "endLine": 50, "endColumn": 27}, {"ruleId": "433", "severity": 1, "message": "454", "line": 29, "column": 10, "nodeType": "435", "messageId": "436", "endLine": 29, "endColumn": 25}, {"ruleId": "433", "severity": 1, "message": "456", "line": 49, "column": 9, "nodeType": "435", "messageId": "436", "endLine": 49, "endColumn": 21}, {"ruleId": "433", "severity": 1, "message": "457", "line": 52, "column": 9, "nodeType": "435", "messageId": "436", "endLine": 52, "endColumn": 21}, {"ruleId": "433", "severity": 1, "message": "458", "line": 120, "column": 9, "nodeType": "435", "messageId": "436", "endLine": 120, "endColumn": 23}, {"ruleId": "433", "severity": 1, "message": "455", "line": 150, "column": 9, "nodeType": "435", "messageId": "436", "endLine": 150, "endColumn": 27}, {"ruleId": "433", "severity": 1, "message": "459", "line": 8, "column": 3, "nodeType": "435", "messageId": "436", "endLine": 8, "endColumn": 13}, {"ruleId": "433", "severity": 1, "message": "449", "line": 9, "column": 3, "nodeType": "435", "messageId": "436", "endLine": 9, "endColumn": 13}, {"ruleId": "433", "severity": 1, "message": "460", "line": 5, "column": 25, "nodeType": "435", "messageId": "436", "endLine": 5, "endColumn": 34}, {"ruleId": "429", "severity": 1, "message": "461", "line": 36, "column": 6, "nodeType": "431", "endLine": 36, "endColumn": 16, "suggestions": "462"}, {"ruleId": "433", "severity": 1, "message": "463", "line": 86, "column": 9, "nodeType": "435", "messageId": "436", "endLine": 86, "endColumn": 20}, {"ruleId": "433", "severity": 1, "message": "464", "line": 2, "column": 3, "nodeType": "435", "messageId": "436", "endLine": 2, "endColumn": 12}, {"ruleId": "433", "severity": 1, "message": "465", "line": 22, "column": 29, "nodeType": "435", "messageId": "436", "endLine": 22, "endColumn": 40}, {"ruleId": "433", "severity": 1, "message": "451", "line": 59, "column": 9, "nodeType": "435", "messageId": "436", "endLine": 59, "endColumn": 17}, {"ruleId": "445", "severity": 1, "message": "448", "line": 531, "column": 31, "nodeType": "447", "endLine": 531, "endColumn": 34}, {"ruleId": "433", "severity": 1, "message": "450", "line": 2, "column": 13, "nodeType": "435", "messageId": "436", "endLine": 2, "endColumn": 20}, {"ruleId": "433", "severity": 1, "message": "451", "line": 6, "column": 9, "nodeType": "435", "messageId": "436", "endLine": 6, "endColumn": 17}, {"ruleId": "433", "severity": 1, "message": "450", "line": 6, "column": 13, "nodeType": "435", "messageId": "436", "endLine": 6, "endColumn": 20}, {"ruleId": "433", "severity": 1, "message": "451", "line": 29, "column": 9, "nodeType": "435", "messageId": "436", "endLine": 29, "endColumn": 17}, {"ruleId": "433", "severity": 1, "message": "466", "line": 31, "column": 10, "nodeType": "435", "messageId": "436", "endLine": 31, "endColumn": 17}, {"ruleId": "433", "severity": 1, "message": "458", "line": 116, "column": 9, "nodeType": "435", "messageId": "436", "endLine": 116, "endColumn": 23}, {"ruleId": "429", "severity": 1, "message": "467", "line": 300, "column": 6, "nodeType": "431", "endLine": 300, "endColumn": 51, "suggestions": "468"}, {"ruleId": "445", "severity": 1, "message": "446", "line": 515, "column": 19, "nodeType": "447", "endLine": 522, "endColumn": 20}, {"ruleId": "445", "severity": 1, "message": "446", "line": 535, "column": 23, "nodeType": "447", "endLine": 542, "endColumn": 24}, {"ruleId": "445", "severity": 1, "message": "446", "line": 550, "column": 19, "nodeType": "447", "endLine": 557, "endColumn": 20}, {"ruleId": "433", "severity": 1, "message": "469", "line": 1, "column": 10, "nodeType": "435", "messageId": "436", "endLine": 1, "endColumn": 13}, {"ruleId": "433", "severity": 1, "message": "449", "line": 1, "column": 29, "nodeType": "435", "messageId": "436", "endLine": 1, "endColumn": 39}, {"ruleId": "433", "severity": 1, "message": "465", "line": 4, "column": 29, "nodeType": "435", "messageId": "436", "endLine": 4, "endColumn": 40}, {"ruleId": "445", "severity": 1, "message": "448", "line": 215, "column": 17, "nodeType": "447", "endLine": 219, "endColumn": 52}, {"ruleId": "433", "severity": 1, "message": "465", "line": 3, "column": 10, "nodeType": "435", "messageId": "436", "endLine": 3, "endColumn": 21}, {"ruleId": "433", "severity": 1, "message": "470", "line": 10, "column": 8, "nodeType": "435", "messageId": "436", "endLine": 10, "endColumn": 13}, {"ruleId": "433", "severity": 1, "message": "471", "line": 22, "column": 10, "nodeType": "435", "messageId": "436", "endLine": 22, "endColumn": 19}, {"ruleId": "433", "severity": 1, "message": "472", "line": 27, "column": 17, "nodeType": "435", "messageId": "436", "endLine": 27, "endColumn": 25}, {"ruleId": "445", "severity": 1, "message": "446", "line": 203, "column": 17, "nodeType": "447", "endLine": 203, "endColumn": 89}, {"ruleId": "433", "severity": 1, "message": "473", "line": 13, "column": 8, "nodeType": "435", "messageId": "436", "endLine": 13, "endColumn": 19}, {"ruleId": "433", "severity": 1, "message": "465", "line": 4, "column": 10, "nodeType": "435", "messageId": "436", "endLine": 4, "endColumn": 21}, {"ruleId": "433", "severity": 1, "message": "474", "line": 10, "column": 8, "nodeType": "435", "messageId": "436", "endLine": 10, "endColumn": 19}, {"ruleId": "433", "severity": 1, "message": "470", "line": 12, "column": 8, "nodeType": "435", "messageId": "436", "endLine": 12, "endColumn": 13}, {"ruleId": "433", "severity": 1, "message": "475", "line": 15, "column": 9, "nodeType": "435", "messageId": "436", "endLine": 15, "endColumn": 17}, {"ruleId": "433", "severity": 1, "message": "476", "line": 63, "column": 9, "nodeType": "435", "messageId": "436", "endLine": 63, "endColumn": 33}, {"ruleId": "433", "severity": 1, "message": "465", "line": 4, "column": 29, "nodeType": "435", "messageId": "436", "endLine": 4, "endColumn": 40}, {"ruleId": "433", "severity": 1, "message": "477", "line": 6, "column": 10, "nodeType": "435", "messageId": "436", "endLine": 6, "endColumn": 15}, {"ruleId": "433", "severity": 1, "message": "470", "line": 30, "column": 8, "nodeType": "435", "messageId": "436", "endLine": 30, "endColumn": 13}, {"ruleId": "429", "severity": 1, "message": "478", "line": 69, "column": 6, "nodeType": "431", "endLine": 69, "endColumn": 8, "suggestions": "479"}, {"ruleId": "445", "severity": 1, "message": "448", "line": 19, "column": 11, "nodeType": "447", "endLine": 24, "endColumn": 12}, {"ruleId": "445", "severity": 1, "message": "448", "line": 30, "column": 11, "nodeType": "447", "endLine": 35, "endColumn": 12}, {"ruleId": "445", "severity": 1, "message": "448", "line": 41, "column": 11, "nodeType": "447", "endLine": 46, "endColumn": 12}, {"ruleId": "445", "severity": 1, "message": "448", "line": 52, "column": 11, "nodeType": "447", "endLine": 57, "endColumn": 12}, {"ruleId": "445", "severity": 1, "message": "448", "line": 63, "column": 11, "nodeType": "447", "endLine": 68, "endColumn": 12}, {"ruleId": "445", "severity": 1, "message": "448", "line": 73, "column": 11, "nodeType": "447", "endLine": 78, "endColumn": 12}, {"ruleId": "480", "severity": 1, "message": "481", "line": 10, "column": 3, "nodeType": "482", "messageId": "483", "endLine": 10, "endColumn": 17}, {"ruleId": "480", "severity": 1, "message": "484", "line": 11, "column": 3, "nodeType": "482", "messageId": "483", "endLine": 11, "endColumn": 18}, {"ruleId": "480", "severity": 1, "message": "485", "line": 48, "column": 3, "nodeType": "482", "messageId": "483", "endLine": 48, "endColumn": 28}, {"ruleId": "445", "severity": 1, "message": "446", "line": 171, "column": 15, "nodeType": "447", "endLine": 171, "endColumn": 49}, {"ruleId": "445", "severity": 1, "message": "446", "line": 176, "column": 15, "nodeType": "447", "endLine": 176, "endColumn": 49}, {"ruleId": "445", "severity": 1, "message": "446", "line": 181, "column": 15, "nodeType": "447", "endLine": 181, "endColumn": 49}, {"ruleId": "445", "severity": 1, "message": "446", "line": 186, "column": 15, "nodeType": "447", "endLine": 186, "endColumn": 49}, {"ruleId": "445", "severity": 1, "message": "446", "line": 191, "column": 15, "nodeType": "447", "endLine": 191, "endColumn": 49}, {"ruleId": "433", "severity": 1, "message": "438", "line": 2, "column": 16, "nodeType": "435", "messageId": "436", "endLine": 2, "endColumn": 19}, {"ruleId": "433", "severity": 1, "message": "486", "line": 10, "column": 10, "nodeType": "435", "messageId": "436", "endLine": 10, "endColumn": 26}, {"ruleId": "433", "severity": 1, "message": "487", "line": 33, "column": 10, "nodeType": "435", "messageId": "436", "endLine": 33, "endColumn": 20}, {"ruleId": "429", "severity": 1, "message": "488", "line": 80, "column": 6, "nodeType": "431", "endLine": 80, "endColumn": 8, "suggestions": "489"}, {"ruleId": "429", "severity": 1, "message": "490", "line": 84, "column": 6, "nodeType": "431", "endLine": 84, "endColumn": 20, "suggestions": "491"}, {"ruleId": "492", "severity": 1, "message": "493", "line": 119, "column": 29, "nodeType": "494", "messageId": "483", "endLine": 119, "endColumn": 31}, {"ruleId": "429", "severity": 1, "message": "488", "line": 142, "column": 6, "nodeType": "431", "endLine": 142, "endColumn": 44, "suggestions": "495"}, {"ruleId": "492", "severity": 1, "message": "493", "line": 417, "column": 46, "nodeType": "494", "messageId": "483", "endLine": 417, "endColumn": 48}, {"ruleId": "492", "severity": 1, "message": "493", "line": 430, "column": 38, "nodeType": "494", "messageId": "483", "endLine": 430, "endColumn": 40}, {"ruleId": "433", "severity": 1, "message": "496", "line": 16, "column": 3, "nodeType": "435", "messageId": "436", "endLine": 16, "endColumn": 11}, {"ruleId": "433", "severity": 1, "message": "497", "line": 24, "column": 3, "nodeType": "435", "messageId": "436", "endLine": 24, "endColumn": 11}, {"ruleId": "498", "severity": 1, "message": "499", "line": 37, "column": 1, "nodeType": "500", "endLine": 108, "endColumn": 3}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.", "ArrayExpression", ["501"], "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'Line' is defined but never used.", "'Bar' is defined but never used.", "'Pie' is defined but never used.", "'Doughnut' is defined but never used.", "'AccountManagement' is defined but never used.", "'ListFeedbackAdminPage' is defined but never used.", "'setSidebarCollapsed' is assigned a value but never used.", "'setNotifications' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "'InputGroup' is defined but never used.", "'Routers' is defined but never used.", "'navigate' is assigned a value but never used.", "'setReviews' is assigned a value but never used.", "'useRef' is defined but never used.", "'showDeleteModal' is assigned a value but never used.", "'handleLockCustomer' is assigned a value but never used.", "'handleAccept' is assigned a value but never used.", "'handleDelete' is assigned a value but never used.", "'getStatusColor' is assigned a value but never used.", "'Pagination' is defined but never used.", "'showToast' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadReportedFeedbacks'. Either include it or remove the dependency array.", ["502"], "'getSeverity' is assigned a value but never used.", "'Container' is defined but never used.", "'FaArrowLeft' is defined but never used.", "'loading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchPayments'. Either include it or remove the dependency array.", ["503"], "'Col' is defined but never used.", "'axios' is defined but never used.", "'isLoading' is assigned a value but never used.", "'setEmail' is assigned a value but never used.", "'GoogleLogin' is defined but never used.", "'AuthActions' is defined but never used.", "'dispatch' is assigned a value but never used.", "'togglePasswordVisibility' is assigned a value but never used.", "'Route' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchRefunds'. Either include it or remove the dependency array.", ["504"], "no-dupe-keys", "Duplicate key 'UPDATE_PROFILE'.", "ObjectExpression", "unexpected", "Duplicate key 'CHANGE_PASSWORD'.", "Duplicate key 'FETCH_FEEDBACK_BY_HOTELID'.", "'initializeSocket' is defined but never used.", "'isReadLast' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAllUser'. Either include it or remove the dependency array.", ["505"], "React Hook useEffect has a missing dependency: 'fetchHistoryChat'. Either include it or remove the dependency array.", ["506"], "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", ["507"], "'Dropdown' is defined but never used.", "'FaFilter' is defined but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", {"desc": "508", "fix": "509"}, {"desc": "510", "fix": "511"}, {"desc": "512", "fix": "513"}, {"desc": "514", "fix": "515"}, {"desc": "516", "fix": "517"}, {"desc": "518", "fix": "519"}, {"desc": "520", "fix": "521"}, "Update the dependencies array to be: [Auth?._id, dispatch]", {"range": "522", "text": "523"}, "Update the dependencies array to be: [dispatch, loadReportedFeedbacks]", {"range": "524", "text": "525"}, "Update the dependencies array to be: [selected<PERSON><PERSON><PERSON>, selected<PERSON>ear, selectedStatus, fetchPayments]", {"range": "526", "text": "527"}, "Update the dependencies array to be: [fetchRefunds]", {"range": "528", "text": "529"}, "Update the dependencies array to be: [fetchAllUser]", {"range": "530", "text": "531"}, "Update the dependencies array to be: [fetchHistoryChat, selectedUser]", {"range": "532", "text": "533"}, "Update the dependencies array to be: [Socket, Auth._id, selectedUser?._id, fetchAllUser]", {"range": "534", "text": "535"}, [1916, 1927], "[Auth?._id, dispatch]", [1162, 1172], "[dispatch, loadReportedFeedbacks]", [11004, 11049], "[selected<PERSON><PERSON><PERSON>, selected<PERSON><PERSON>, selected<PERSON>tatus, fetchPayments]", [1864, 1866], "[fetchRefunds]", [2772, 2774], "[fetchAllUser]", [2831, 2845], "[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>]", [4480, 4518], "[Socket, Auth._id, selectedUser?._id, fetchAllUser]"]