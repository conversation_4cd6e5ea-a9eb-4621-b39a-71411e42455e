[{"E:\\Uroom\\Customer\\src\\index.js": "1", "E:\\Uroom\\Customer\\src\\App.js": "2", "E:\\Uroom\\Customer\\src\\reportWebVitals.js": "3", "E:\\Uroom\\Customer\\src\\redux\\store.js": "4", "E:\\Uroom\\Customer\\src\\utils\\Utils.js": "5", "E:\\Uroom\\Customer\\src\\redux\\socket\\socketSlice.js": "6", "E:\\Uroom\\Customer\\src\\redux\\root-reducer.js": "7", "E:\\Uroom\\Customer\\src\\redux\\root-saga.js": "8", "E:\\Uroom\\Customer\\src\\utils\\Routes.js": "9", "E:\\Uroom\\Customer\\src\\pages\\ErrorPage.jsx": "10", "E:\\Uroom\\Customer\\src\\pages\\BannedPage.jsx": "11", "E:\\Uroom\\Customer\\src\\pages\\customer\\home\\HomePage.jsx": "12", "E:\\Uroom\\Customer\\src\\pages\\customer\\home\\HotelSearchPage.jsx": "13", "E:\\Uroom\\Customer\\src\\pages\\customer\\home\\BookingCheckPage.jsx": "14", "E:\\Uroom\\Customer\\src\\pages\\customer\\home\\HomeDetailPage.jsx": "15", "E:\\Uroom\\Customer\\src\\pages\\customer\\home\\PaymentPage.jsx": "16", "E:\\Uroom\\Customer\\src\\pages\\customer\\home\\PaymentSuccessPage.jsx": "17", "E:\\Uroom\\Customer\\src\\pages\\customer\\home\\RoomDetailPage.jsx": "18", "E:\\Uroom\\Customer\\src\\pages\\customer\\home\\ReportedFeedback.jsx": "19", "E:\\Uroom\\Customer\\src\\pages\\customer\\home\\PaymentFailedPage.jsx": "20", "E:\\Uroom\\Customer\\src\\pages\\customer\\home\\ChatPage.jsx": "21", "E:\\Uroom\\Customer\\src\\pages\\customer\\login_register\\VerifyCodeRegisterPage.jsx": "22", "E:\\Uroom\\Customer\\src\\pages\\customer\\login_register\\RegisterPage.jsx": "23", "E:\\Uroom\\Customer\\src\\pages\\customer\\login_register\\LoginPage.jsx": "24", "E:\\Uroom\\Customer\\src\\pages\\customer\\login_register\\ForgetPasswordPage.jsx": "25", "E:\\Uroom\\Customer\\src\\pages\\customer\\login_register\\VerifyCodePage.jsx": "26", "E:\\Uroom\\Customer\\src\\pages\\customer\\login_register\\ResetPasswordPage.jsx": "27", "E:\\Uroom\\Customer\\src\\pages\\customer\\information\\MyAccountPage.jsx": "28", "E:\\Uroom\\Customer\\src\\pages\\customer\\information\\CreateFeedback.jsx": "29", "E:\\Uroom\\Customer\\src\\pages\\customer\\information\\BookingBill.jsx": "30", "E:\\Uroom\\Customer\\src\\utils\\data.js": "31", "E:\\Uroom\\Customer\\src\\utils\\qaData.js": "32", "E:\\Uroom\\Customer\\src\\redux\\auth\\reducer.js": "33", "E:\\Uroom\\Customer\\src\\redux\\hotel\\reducer.js": "34", "E:\\Uroom\\Customer\\src\\redux\\search\\reducer.js": "35", "E:\\Uroom\\Customer\\src\\redux\\auth\\saga.js": "36", "E:\\Uroom\\Customer\\src\\redux\\search\\saga.js": "37", "E:\\Uroom\\Customer\\src\\redux\\reservations\\saga.js": "38", "E:\\Uroom\\Customer\\src\\redux\\search\\actions.js": "39", "E:\\Uroom\\Customer\\src\\redux\\room\\reducer.js": "40", "E:\\Uroom\\Customer\\src\\redux\\reservations\\reducer.js": "41", "E:\\Uroom\\Customer\\src\\redux\\feedback\\saga.js": "42", "E:\\Uroom\\Customer\\src\\redux\\hotel\\saga.js": "43", "E:\\Uroom\\Customer\\src\\redux\\room\\saga.js": "44", "E:\\Uroom\\Customer\\src\\redux\\feedback\\reducer.js": "45", "E:\\Uroom\\Customer\\src\\redux\\reportedFeedback\\reducer.js": "46", "E:\\Uroom\\Customer\\src\\redux\\message\\reducer.js": "47", "E:\\Uroom\\Customer\\src\\redux\\message\\saga.js": "48", "E:\\Uroom\\Customer\\src\\redux\\hotel\\actions.js": "49", "E:\\Uroom\\Customer\\src\\redux\\reportedFeedback\\saga.js": "50", "E:\\Uroom\\Customer\\src\\redux\\room\\actions.js": "51", "E:\\Uroom\\Customer\\src\\redux\\search\\factories.js": "52", "E:\\Uroom\\Customer\\src\\redux\\auth\\actions.js": "53", "E:\\Uroom\\Customer\\src\\pages\\customer\\Header.jsx": "54", "E:\\Uroom\\Customer\\src\\pages\\customer\\Footer.jsx": "55", "E:\\Uroom\\Customer\\src\\utils\\apiConfig.js": "56", "E:\\Uroom\\Customer\\src\\pages\\MapLocation.jsx": "57", "E:\\Uroom\\Customer\\src\\redux\\chatbox\\reducer.js": "58", "E:\\Uroom\\Customer\\src\\redux\\feedback\\actions.js": "59", "E:\\Uroom\\Customer\\src\\redux\\reportedFeedback\\actions.js": "60", "E:\\Uroom\\Customer\\src\\redux\\chatbox\\actions.js": "61", "E:\\Uroom\\Customer\\src\\redux\\feedback\\factories.js": "62", "E:\\Uroom\\Customer\\src\\redux\\message\\actions.js": "63", "E:\\Uroom\\Customer\\src\\redux\\promotion\\reducer.js": "64", "E:\\Uroom\\Customer\\src\\components\\ErrorModal.jsx": "65", "E:\\Uroom\\Customer\\src\\redux\\promotion\\saga.js": "66", "E:\\Uroom\\Customer\\src\\components\\ConfirmationModal.jsx": "67", "E:\\Uroom\\Customer\\src\\components\\ToastContainer.jsx": "68", "E:\\Uroom\\Customer\\src\\components\\Pagination.jsx": "69", "E:\\Uroom\\Customer\\src\\pages\\customer\\home\\components\\PromotionModal.jsx": "70", "E:\\Uroom\\Customer\\src\\pages\\customer\\home\\components\\HotelClosedModal.jsx": "71", "E:\\Uroom\\Customer\\src\\pages\\customer\\home\\components\\PromotionErrorModal.jsx": "72", "E:\\Uroom\\Customer\\src\\pages\\customer\\home\\components\\RoomClosedModal.jsx": "73", "E:\\Uroom\\Customer\\src\\redux\\reservation\\factories.js": "74", "E:\\Uroom\\Customer\\src\\utils\\handleToken.js": "75", "E:\\Uroom\\Customer\\src\\redux\\auth\\factories.js": "76", "E:\\Uroom\\Customer\\src\\pages\\customer\\login_register\\GoogleLogin.jsx": "77", "E:\\Uroom\\Customer\\src\\redux\\reservations\\actions.js": "78", "E:\\Uroom\\Customer\\src\\utils\\fonts.js": "79", "E:\\Uroom\\Customer\\src\\pages\\customer\\information\\components\\ViewInformation.jsx": "80", "E:\\Uroom\\Customer\\src\\pages\\customer\\information\\components\\ViewAvatar.jsx": "81", "E:\\Uroom\\Customer\\src\\pages\\customer\\information\\components\\MyFeedback.jsx": "82", "E:\\Uroom\\Customer\\src\\pages\\customer\\information\\components\\MyFavoriteHotel.jsx": "83", "E:\\Uroom\\Customer\\src\\pages\\customer\\information\\components\\BookingHistory.jsx": "84", "E:\\Uroom\\Customer\\src\\pages\\customer\\information\\components\\RefundReservation.jsx": "85", "E:\\Uroom\\Customer\\src\\pages\\customer\\information\\components\\MyPromotion.jsx": "86", "E:\\Uroom\\Customer\\src\\pages\\customer\\information\\components\\MyReportFeedBack.jsx": "87", "E:\\Uroom\\Customer\\src\\pages\\customer\\information\\components\\ChangePassword.jsx": "88", "E:\\Uroom\\Customer\\src\\redux\\reservations\\factories.js": "89", "E:\\Uroom\\Customer\\src\\redux\\reportedFeedback\\factories.js": "90", "E:\\Uroom\\Customer\\src\\redux\\room\\factories.js": "91", "E:\\Uroom\\Customer\\src\\redux\\hotel\\factories.js": "92", "E:\\Uroom\\Customer\\src\\redux\\message\\factories.js": "93", "E:\\Uroom\\Customer\\src\\adapter\\ApiConstants.js": "94", "E:\\Uroom\\Customer\\src\\services\\NotificationService.js": "95", "E:\\Uroom\\Customer\\src\\libs\\api\\index.js": "96", "E:\\Uroom\\Customer\\src\\redux\\promotion\\factories.js": "97", "E:\\Uroom\\Customer\\src\\libs\\firebaseConfig.js": "98", "E:\\Uroom\\Customer\\src\\redux\\promotion\\actions.js": "99", "E:\\Uroom\\Customer\\src\\pages\\customer\\home\\components\\CancelReservationModal.jsx": "100", "E:\\Uroom\\Customer\\src\\redux\\refunding_reservation\\factories.js": "101"}, {"size": 831, "mtime": 1753036196703, "results": "102", "hashOfConfig": "103"}, {"size": 4850, "mtime": 1753036196663, "results": "104", "hashOfConfig": "103"}, {"size": 375, "mtime": 1753036196726, "results": "105", "hashOfConfig": "103"}, {"size": 1274, "mtime": 1753036196726, "results": "106", "hashOfConfig": "103"}, {"size": 3227, "mtime": 1753036196727, "results": "107", "hashOfConfig": "103"}, {"size": 1451, "mtime": 1753036196726, "results": "108", "hashOfConfig": "103"}, {"size": 1006, "mtime": 1753036196725, "results": "109", "hashOfConfig": "103"}, {"size": 731, "mtime": 1753036196725, "results": "110", "hashOfConfig": "103"}, {"size": 1300, "mtime": 1753036196727, "results": "111", "hashOfConfig": "103"}, {"size": 1374, "mtime": 1753036196704, "results": "112", "hashOfConfig": "103"}, {"size": 3588, "mtime": 1753036196703, "results": "113", "hashOfConfig": "103"}, {"size": 41908, "mtime": 1753036196707, "results": "114", "hashOfConfig": "103"}, {"size": 38585, "mtime": 1753036196707, "results": "115", "hashOfConfig": "103"}, {"size": 42010, "mtime": 1753036196705, "results": "116", "hashOfConfig": "103"}, {"size": 88624, "mtime": 1753036196706, "results": "117", "hashOfConfig": "103"}, {"size": 7041, "mtime": 1753036196708, "results": "118", "hashOfConfig": "103"}, {"size": 4608, "mtime": 1753036196708, "results": "119", "hashOfConfig": "103"}, {"size": 51823, "mtime": 1753036196709, "results": "120", "hashOfConfig": "103"}, {"size": 13701, "mtime": 1753036196708, "results": "121", "hashOfConfig": "103"}, {"size": 3157, "mtime": 1753036196708, "results": "122", "hashOfConfig": "103"}, {"size": 26015, "mtime": 1753036196705, "results": "123", "hashOfConfig": "103"}, {"size": 8504, "mtime": 1753036196717, "results": "124", "hashOfConfig": "103"}, {"size": 7427, "mtime": 1753036196716, "results": "125", "hashOfConfig": "103"}, {"size": 10354, "mtime": 1753036196716, "results": "126", "hashOfConfig": "103"}, {"size": 3742, "mtime": 1753036196715, "results": "127", "hashOfConfig": "103"}, {"size": 7544, "mtime": 1753036196716, "results": "128", "hashOfConfig": "103"}, {"size": 5465, "mtime": 1753036196716, "results": "129", "hashOfConfig": "103"}, {"size": 5583, "mtime": 1753036196712, "results": "130", "hashOfConfig": "103"}, {"size": 16963, "mtime": 1753036196712, "results": "131", "hashOfConfig": "103"}, {"size": 33077, "mtime": 1753036196711, "results": "132", "hashOfConfig": "103"}, {"size": 4408, "mtime": 1753036196728, "results": "133", "hashOfConfig": "103"}, {"size": 10836, "mtime": 1753036196728, "results": "134", "hashOfConfig": "103"}, {"size": 2230, "mtime": 1753036196718, "results": "135", "hashOfConfig": "103"}, {"size": 888, "mtime": 1753036196720, "results": "136", "hashOfConfig": "103"}, {"size": 1043, "mtime": 1753036196726, "results": "137", "hashOfConfig": "103"}, {"size": 9581, "mtime": 1753036196718, "results": "138", "hashOfConfig": "103"}, {"size": 223, "mtime": 1753036196726, "results": "139", "hashOfConfig": "103"}, {"size": 3342, "mtime": 1753036196724, "results": "140", "hashOfConfig": "103"}, {"size": 137, "mtime": 1753036196725, "results": "141", "hashOfConfig": "103"}, {"size": 541, "mtime": 1753036196725, "results": "142", "hashOfConfig": "103"}, {"size": 919, "mtime": 1753036196724, "results": "143", "hashOfConfig": "103"}, {"size": 5057, "mtime": 1753036196719, "results": "144", "hashOfConfig": "103"}, {"size": 4244, "mtime": 1753036196721, "results": "145", "hashOfConfig": "103"}, {"size": 1803, "mtime": 1753036196725, "results": "146", "hashOfConfig": "103"}, {"size": 1136, "mtime": 1753036196719, "results": "147", "hashOfConfig": "103"}, {"size": 917, "mtime": 1753036196723, "results": "148", "hashOfConfig": "103"}, {"size": 551, "mtime": 1753036196721, "results": "149", "hashOfConfig": "103"}, {"size": 2085, "mtime": 1753036196721, "results": "150", "hashOfConfig": "103"}, {"size": 487, "mtime": 1753036196720, "results": "151", "hashOfConfig": "103"}, {"size": 3000, "mtime": 1753036196724, "results": "152", "hashOfConfig": "103"}, {"size": 235, "mtime": 1753036196725, "results": "153", "hashOfConfig": "103"}, {"size": 1669, "mtime": 1753036196726, "results": "154", "hashOfConfig": "103"}, {"size": 1271, "mtime": 1753036196717, "results": "155", "hashOfConfig": "103"}, {"size": 25894, "mtime": 1753036196704, "results": "156", "hashOfConfig": "103"}, {"size": 2571, "mtime": 1753036196704, "results": "157", "hashOfConfig": "103"}, {"size": 346, "mtime": 1753036196728, "results": "158", "hashOfConfig": "103"}, {"size": 2587, "mtime": 1753036196704, "results": "159", "hashOfConfig": "103"}, {"size": 1693, "mtime": 1753036196718, "results": "160", "hashOfConfig": "103"}, {"size": 574, "mtime": 1753036196719, "results": "161", "hashOfConfig": "103"}, {"size": 426, "mtime": 1753036196723, "results": "162", "hashOfConfig": "103"}, {"size": 265, "mtime": 1753036196718, "results": "163", "hashOfConfig": "103"}, {"size": 1536, "mtime": 1753036196719, "results": "164", "hashOfConfig": "103"}, {"size": 322, "mtime": 1753036196721, "results": "165", "hashOfConfig": "103"}, {"size": 3738, "mtime": 1753044895932, "results": "166", "hashOfConfig": "103"}, {"size": 864, "mtime": 1753036196664, "results": "167", "hashOfConfig": "103"}, {"size": 8833, "mtime": 1753044895932, "results": "168", "hashOfConfig": "103"}, {"size": 2348, "mtime": 1753036196664, "results": "169", "hashOfConfig": "103"}, {"size": 1493, "mtime": 1753036196665, "results": "170", "hashOfConfig": "103"}, {"size": 1621, "mtime": 1753036196664, "results": "171", "hashOfConfig": "103"}, {"size": 20662, "mtime": 1753044895931, "results": "172", "hashOfConfig": "103"}, {"size": 1088, "mtime": 1753036196710, "results": "173", "hashOfConfig": "103"}, {"size": 1719, "mtime": 1753036196710, "results": "174", "hashOfConfig": "103"}, {"size": 1178, "mtime": 1753036196711, "results": "175", "hashOfConfig": "103"}, {"size": 402, "mtime": 1753036196724, "results": "176", "hashOfConfig": "103"}, {"size": 551, "mtime": 1753036196728, "results": "177", "hashOfConfig": "103"}, {"size": 1439, "mtime": 1753036196717, "results": "178", "hashOfConfig": "103"}, {"size": 2146, "mtime": 1753036196715, "results": "179", "hashOfConfig": "103"}, {"size": 438, "mtime": 1753036196724, "results": "180", "hashOfConfig": "103"}, {"size": 636, "mtime": 1753036196728, "results": "181", "hashOfConfig": "103"}, {"size": 9884, "mtime": 1753036196715, "results": "182", "hashOfConfig": "103"}, {"size": 5820, "mtime": 1753036196714, "results": "183", "hashOfConfig": "103"}, {"size": 30766, "mtime": 1753036196713, "results": "184", "hashOfConfig": "103"}, {"size": 16495, "mtime": 1753036196713, "results": "185", "hashOfConfig": "103"}, {"size": 27111, "mtime": 1753036196712, "results": "186", "hashOfConfig": "103"}, {"size": 21286, "mtime": 1753036196714, "results": "187", "hashOfConfig": "103"}, {"size": 22920, "mtime": 1753036196713, "results": "188", "hashOfConfig": "103"}, {"size": 14855, "mtime": 1753036196714, "results": "189", "hashOfConfig": "103"}, {"size": 8729, "mtime": 1753036196713, "results": "190", "hashOfConfig": "103"}, {"size": 582, "mtime": 1753036196724, "results": "191", "hashOfConfig": "103"}, {"size": 491, "mtime": 1753036196723, "results": "192", "hashOfConfig": "103"}, {"size": 741, "mtime": 1753036196725, "results": "193", "hashOfConfig": "103"}, {"size": 1071, "mtime": 1753036196720, "results": "194", "hashOfConfig": "103"}, {"size": 377, "mtime": 1753036196721, "results": "195", "hashOfConfig": "103"}, {"size": 2819, "mtime": 1753044895932, "results": "196", "hashOfConfig": "103"}, {"size": 2015, "mtime": 1753036196727, "results": "197", "hashOfConfig": "103"}, {"size": 2658, "mtime": 1753036196703, "results": "198", "hashOfConfig": "103"}, {"size": 341, "mtime": 1753044895817, "results": "199", "hashOfConfig": "103"}, {"size": 693, "mtime": 1753036196703, "results": "200", "hashOfConfig": "103"}, {"size": 1177, "mtime": 1753036196721, "results": "201", "hashOfConfig": "103"}, {"size": 11166, "mtime": 1753036196710, "results": "202", "hashOfConfig": "103"}, {"size": 830, "mtime": 1753036196723, "results": "203", "hashOfConfig": "103"}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1v9qqa3", {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\Uroom\\Customer\\src\\index.js", [], [], "E:\\Uroom\\Customer\\src\\App.js", ["507", "508", "509"], [], "E:\\Uroom\\Customer\\src\\reportWebVitals.js", [], [], "E:\\Uroom\\Customer\\src\\redux\\store.js", [], [], "E:\\Uroom\\Customer\\src\\utils\\Utils.js", [], [], "E:\\Uroom\\Customer\\src\\redux\\socket\\socketSlice.js", [], [], "E:\\Uroom\\Customer\\src\\redux\\root-reducer.js", [], [], "E:\\Uroom\\Customer\\src\\redux\\root-saga.js", [], [], "E:\\Uroom\\Customer\\src\\utils\\Routes.js", [], [], "E:\\Uroom\\Customer\\src\\pages\\ErrorPage.jsx", [], [], "E:\\Uroom\\Customer\\src\\pages\\BannedPage.jsx", [], [], "E:\\Uroom\\Customer\\src\\pages\\customer\\home\\HomePage.jsx", ["510", "511", "512", "513", "514", "515", "516", "517", "518", "519", "520", "521", "522", "523", "524"], [], "E:\\Uroom\\Customer\\src\\pages\\customer\\home\\HotelSearchPage.jsx", ["525"], [], "E:\\Uroom\\Customer\\src\\pages\\customer\\home\\BookingCheckPage.jsx", ["526", "527", "528", "529", "530", "531", "532", "533"], [], "E:\\Uroom\\Customer\\src\\pages\\customer\\home\\HomeDetailPage.jsx", ["534", "535", "536", "537", "538", "539", "540", "541", "542", "543", "544", "545", "546", "547"], [], "E:\\Uroom\\Customer\\src\\pages\\customer\\home\\PaymentPage.jsx", ["548", "549", "550", "551", "552"], [], "E:\\Uroom\\Customer\\src\\pages\\customer\\home\\PaymentSuccessPage.jsx", ["553", "554"], [], "E:\\Uroom\\Customer\\src\\pages\\customer\\home\\RoomDetailPage.jsx", ["555", "556", "557", "558", "559"], [], "E:\\Uroom\\Customer\\src\\pages\\customer\\home\\ReportedFeedback.jsx", ["560", "561", "562"], [], "E:\\Uroom\\Customer\\src\\pages\\customer\\home\\PaymentFailedPage.jsx", ["563", "564"], [], "E:\\Uroom\\Customer\\src\\pages\\customer\\home\\ChatPage.jsx", ["565", "566", "567", "568", "569", "570", "571"], [], "E:\\Uroom\\Customer\\src\\pages\\customer\\login_register\\VerifyCodeRegisterPage.jsx", [], [], "E:\\Uroom\\Customer\\src\\pages\\customer\\login_register\\RegisterPage.jsx", ["572", "573"], [], "E:\\Uroom\\Customer\\src\\pages\\customer\\login_register\\LoginPage.jsx", ["574", "575"], [], "E:\\Uroom\\Customer\\src\\pages\\customer\\login_register\\ForgetPasswordPage.jsx", ["576", "577", "578", "579", "580"], [], "E:\\Uroom\\Customer\\src\\pages\\customer\\login_register\\VerifyCodePage.jsx", ["581", "582", "583", "584", "585"], [], "E:\\Uroom\\Customer\\src\\pages\\customer\\login_register\\ResetPasswordPage.jsx", ["586", "587", "588"], [], "E:\\Uroom\\Customer\\src\\pages\\customer\\information\\MyAccountPage.jsx", ["589", "590", "591", "592", "593", "594", "595", "596", "597", "598", "599", "600", "601"], [], "E:\\Uroom\\Customer\\src\\pages\\customer\\information\\CreateFeedback.jsx", ["602", "603", "604", "605"], [], "E:\\Uroom\\Customer\\src\\pages\\customer\\information\\BookingBill.jsx", ["606", "607", "608", "609", "610", "611", "612"], [], "E:\\Uroom\\Customer\\src\\utils\\data.js", [], [], "E:\\Uroom\\Customer\\src\\utils\\qaData.js", ["613"], [], "E:\\Uroom\\Customer\\src\\redux\\auth\\reducer.js", [], [], "E:\\Uroom\\Customer\\src\\redux\\hotel\\reducer.js", [], [], "E:\\Uroom\\Customer\\src\\redux\\search\\reducer.js", [], [], "E:\\Uroom\\Customer\\src\\redux\\auth\\saga.js", [], [], "E:\\Uroom\\Customer\\src\\redux\\search\\saga.js", ["614", "615", "616", "617", "618", "619"], [], "E:\\Uroom\\Customer\\src\\redux\\reservations\\saga.js", [], [], "E:\\Uroom\\Customer\\src\\redux\\search\\actions.js", [], [], "E:\\Uroom\\Customer\\src\\redux\\room\\reducer.js", [], [], "E:\\Uroom\\Customer\\src\\redux\\reservations\\reducer.js", [], [], "E:\\Uroom\\Customer\\src\\redux\\feedback\\saga.js", [], [], "E:\\Uroom\\Customer\\src\\redux\\hotel\\saga.js", [], [], "E:\\Uroom\\Customer\\src\\redux\\room\\saga.js", ["620", "621"], [], "E:\\Uroom\\Customer\\src\\redux\\feedback\\reducer.js", [], [], "E:\\Uroom\\Customer\\src\\redux\\reportedFeedback\\reducer.js", [], [], "E:\\Uroom\\Customer\\src\\redux\\message\\reducer.js", [], [], "E:\\Uroom\\Customer\\src\\redux\\message\\saga.js", [], [], "E:\\Uroom\\Customer\\src\\redux\\hotel\\actions.js", [], [], "E:\\Uroom\\Customer\\src\\redux\\reportedFeedback\\saga.js", ["622"], [], "E:\\Uroom\\Customer\\src\\redux\\room\\actions.js", [], [], "E:\\Uroom\\Customer\\src\\redux\\search\\factories.js", [], [], "E:\\Uroom\\Customer\\src\\redux\\auth\\actions.js", [], [], "E:\\Uroom\\Customer\\src\\pages\\customer\\Header.jsx", ["623", "624", "625", "626", "627", "628", "629", "630", "631", "632", "633", "634"], [], "E:\\Uroom\\Customer\\src\\pages\\customer\\Footer.jsx", ["635", "636", "637"], [], "E:\\Uroom\\Customer\\src\\utils\\apiConfig.js", [], [], "E:\\Uroom\\Customer\\src\\pages\\MapLocation.jsx", ["638"], [], "E:\\Uroom\\Customer\\src\\redux\\chatbox\\reducer.js", [], [], "E:\\Uroom\\Customer\\src\\redux\\feedback\\actions.js", [], [], "E:\\Uroom\\Customer\\src\\redux\\reportedFeedback\\actions.js", [], [], "E:\\Uroom\\Customer\\src\\redux\\chatbox\\actions.js", [], [], "E:\\Uroom\\Customer\\src\\redux\\feedback\\factories.js", [], [], "E:\\Uroom\\Customer\\src\\redux\\message\\actions.js", [], [], "E:\\Uroom\\Customer\\src\\redux\\promotion\\reducer.js", ["639"], [], "E:\\Uroom\\Customer\\src\\components\\ErrorModal.jsx", [], [], "E:\\Uroom\\Customer\\src\\redux\\promotion\\saga.js", [], [], "E:\\Uroom\\Customer\\src\\components\\ConfirmationModal.jsx", [], [], "E:\\Uroom\\Customer\\src\\components\\ToastContainer.jsx", [], [], "E:\\Uroom\\Customer\\src\\components\\Pagination.jsx", [], [], "E:\\Uroom\\Customer\\src\\pages\\customer\\home\\components\\PromotionModal.jsx", ["640", "641", "642", "643"], [], "E:\\Uroom\\Customer\\src\\pages\\customer\\home\\components\\HotelClosedModal.jsx", [], [], "E:\\Uroom\\Customer\\src\\pages\\customer\\home\\components\\PromotionErrorModal.jsx", [], [], "E:\\Uroom\\Customer\\src\\pages\\customer\\home\\components\\RoomClosedModal.jsx", [], [], "E:\\Uroom\\Customer\\src\\redux\\reservation\\factories.js", [], [], "E:\\Uroom\\Customer\\src\\utils\\handleToken.js", [], [], "E:\\Uroom\\Customer\\src\\redux\\auth\\factories.js", [], [], "E:\\Uroom\\Customer\\src\\pages\\customer\\login_register\\GoogleLogin.jsx", [], [], "E:\\Uroom\\Customer\\src\\redux\\reservations\\actions.js", [], [], "E:\\Uroom\\Customer\\src\\utils\\fonts.js", [], [], "E:\\Uroom\\Customer\\src\\pages\\customer\\information\\components\\ViewInformation.jsx", ["644"], [], "E:\\Uroom\\Customer\\src\\pages\\customer\\information\\components\\ViewAvatar.jsx", ["645", "646"], [], "E:\\Uroom\\Customer\\src\\pages\\customer\\information\\components\\MyFeedback.jsx", ["647", "648", "649", "650"], [], "E:\\Uroom\\Customer\\src\\pages\\customer\\information\\components\\MyFavoriteHotel.jsx", ["651", "652"], [], "E:\\Uroom\\Customer\\src\\pages\\customer\\information\\components\\BookingHistory.jsx", ["653", "654", "655", "656", "657", "658", "659", "660"], [], "E:\\Uroom\\Customer\\src\\pages\\customer\\information\\components\\RefundReservation.jsx", ["661", "662", "663", "664", "665"], [], "E:\\Uroom\\Customer\\src\\pages\\customer\\information\\components\\MyPromotion.jsx", ["666", "667"], [], "E:\\Uroom\\Customer\\src\\pages\\customer\\information\\components\\MyReportFeedBack.jsx", ["668", "669", "670", "671", "672"], [], "E:\\Uroom\\Customer\\src\\pages\\customer\\information\\components\\ChangePassword.jsx", ["673", "674"], [], "E:\\Uroom\\Customer\\src\\redux\\reservations\\factories.js", [], [], "E:\\Uroom\\Customer\\src\\redux\\reportedFeedback\\factories.js", [], [], "E:\\Uroom\\Customer\\src\\redux\\room\\factories.js", [], [], "E:\\Uroom\\Customer\\src\\redux\\hotel\\factories.js", ["675", "676", "677"], [], "E:\\Uroom\\Customer\\src\\redux\\message\\factories.js", [], [], "E:\\Uroom\\Customer\\src\\adapter\\ApiConstants.js", ["678", "679", "680"], [], "E:\\Uroom\\Customer\\src\\services\\NotificationService.js", [], [], "E:\\Uroom\\Customer\\src\\libs\\api\\index.js", ["681"], [], "E:\\Uroom\\Customer\\src\\redux\\promotion\\factories.js", [], [], "E:\\Uroom\\Customer\\src\\libs\\firebaseConfig.js", [], [], "E:\\Uroom\\Customer\\src\\redux\\promotion\\actions.js", [], [], "E:\\Uroom\\Customer\\src\\pages\\customer\\home\\components\\CancelReservationModal.jsx", ["682", "683", "684", "685"], [], "E:\\Uroom\\Customer\\src\\redux\\refunding_reservation\\factories.js", [], [], {"ruleId": "686", "severity": 1, "message": "687", "line": 3, "column": 8, "nodeType": "688", "messageId": "689", "endLine": 3, "endColumn": 13}, {"ruleId": "690", "severity": 1, "message": "691", "line": 46, "column": 6, "nodeType": "692", "endLine": 46, "endColumn": 17, "suggestions": "693"}, {"ruleId": "694", "severity": 1, "message": "695", "line": 103, "column": 61, "nodeType": "696", "messageId": "697", "endLine": 103, "endColumn": 76}, {"ruleId": "686", "severity": 1, "message": "698", "line": 33, "column": 8, "nodeType": "688", "messageId": "689", "endLine": 33, "endColumn": 14}, {"ruleId": "686", "severity": 1, "message": "699", "line": 34, "column": 8, "nodeType": "688", "messageId": "689", "endLine": 34, "endColumn": 14}, {"ruleId": "686", "severity": 1, "message": "700", "line": 35, "column": 8, "nodeType": "688", "messageId": "689", "endLine": 35, "endColumn": 14}, {"ruleId": "686", "severity": 1, "message": "701", "line": 63, "column": 8, "nodeType": "688", "messageId": "689", "endLine": 63, "endColumn": 19}, {"ruleId": "686", "severity": 1, "message": "687", "line": 64, "column": 8, "nodeType": "688", "messageId": "689", "endLine": 64, "endColumn": 13}, {"ruleId": "686", "severity": 1, "message": "702", "line": 87, "column": 10, "nodeType": "688", "messageId": "689", "endLine": 87, "endColumn": 16}, {"ruleId": "690", "severity": 1, "message": "691", "line": 97, "column": 6, "nodeType": "692", "endLine": 97, "endColumn": 8, "suggestions": "703"}, {"ruleId": "686", "severity": 1, "message": "704", "line": 165, "column": 10, "nodeType": "688", "messageId": "689", "endLine": 165, "endColumn": 16}, {"ruleId": "705", "severity": 1, "message": "706", "line": 638, "column": 24, "nodeType": "707", "messageId": "708", "endLine": 638, "endColumn": 26}, {"ruleId": "705", "severity": 1, "message": "709", "line": 665, "column": 26, "nodeType": "707", "messageId": "708", "endLine": 665, "endColumn": 28}, {"ruleId": "705", "severity": 1, "message": "709", "line": 675, "column": 26, "nodeType": "707", "messageId": "708", "endLine": 675, "endColumn": 28}, {"ruleId": "705", "severity": 1, "message": "709", "line": 685, "column": 26, "nodeType": "707", "messageId": "708", "endLine": 685, "endColumn": 28}, {"ruleId": "705", "severity": 1, "message": "706", "line": 907, "column": 29, "nodeType": "707", "messageId": "708", "endLine": 907, "endColumn": 31}, {"ruleId": "686", "severity": 1, "message": "710", "line": 1004, "column": 10, "nodeType": "688", "messageId": "689", "endLine": 1004, "endColumn": 25}, {"ruleId": "705", "severity": 1, "message": "706", "line": 1203, "column": 39, "nodeType": "707", "messageId": "708", "endLine": 1203, "endColumn": 41}, {"ruleId": "711", "severity": 1, "message": "712", "line": 835, "column": 37, "nodeType": "696", "endLine": 842, "endColumn": 38}, {"ruleId": "686", "severity": 1, "message": "713", "line": 1, "column": 38, "nodeType": "688", "messageId": "689", "endLine": 1, "endColumn": 41}, {"ruleId": "686", "severity": 1, "message": "714", "line": 10, "column": 3, "nodeType": "688", "messageId": "689", "endLine": 10, "endColumn": 13}, {"ruleId": "686", "severity": 1, "message": "715", "line": 17, "column": 13, "nodeType": "688", "messageId": "689", "endLine": 17, "endColumn": 20}, {"ruleId": "686", "severity": 1, "message": "716", "line": 37, "column": 10, "nodeType": "688", "messageId": "689", "endLine": 37, "endColumn": 15}, {"ruleId": "686", "severity": 1, "message": "717", "line": 37, "column": 17, "nodeType": "688", "messageId": "689", "endLine": 37, "endColumn": 25}, {"ruleId": "690", "severity": 1, "message": "718", "line": 274, "column": 6, "nodeType": "692", "endLine": 274, "endColumn": 77, "suggestions": "719"}, {"ruleId": "711", "severity": 1, "message": "712", "line": 745, "column": 21, "nodeType": "696", "endLine": 749, "endColumn": 22}, {"ruleId": "711", "severity": 1, "message": "712", "line": 783, "column": 23, "nodeType": "696", "endLine": 797, "endColumn": 24}, {"ruleId": "686", "severity": 1, "message": "720", "line": 6, "column": 3, "nodeType": "688", "messageId": "689", "endLine": 6, "endColumn": 8}, {"ruleId": "686", "severity": 1, "message": "721", "line": 159, "column": 10, "nodeType": "688", "messageId": "689", "endLine": 159, "endColumn": 21}, {"ruleId": "686", "severity": 1, "message": "722", "line": 159, "column": 23, "nodeType": "688", "messageId": "689", "endLine": 159, "endColumn": 37}, {"ruleId": "690", "severity": 1, "message": "723", "line": 180, "column": 6, "nodeType": "692", "endLine": 180, "endColumn": 8, "suggestions": "724"}, {"ruleId": "690", "severity": 1, "message": "725", "line": 315, "column": 6, "nodeType": "692", "endLine": 315, "endColumn": 25, "suggestions": "726"}, {"ruleId": "686", "severity": 1, "message": "727", "line": 866, "column": 9, "nodeType": "688", "messageId": "689", "endLine": 866, "endColumn": 15}, {"ruleId": "705", "severity": 1, "message": "706", "line": 959, "column": 28, "nodeType": "707", "messageId": "708", "endLine": 959, "endColumn": 30}, {"ruleId": "711", "severity": 1, "message": "712", "line": 1071, "column": 19, "nodeType": "696", "endLine": 1082, "endColumn": 20}, {"ruleId": "728", "severity": 1, "message": "729", "line": 1130, "column": 63, "nodeType": "730", "messageId": "731", "endLine": 1130, "endColumn": 65}, {"ruleId": "705", "severity": 1, "message": "706", "line": 1764, "column": 32, "nodeType": "707", "messageId": "708", "endLine": 1764, "endColumn": 34}, {"ruleId": "705", "severity": 1, "message": "709", "line": 2061, "column": 39, "nodeType": "707", "messageId": "708", "endLine": 2061, "endColumn": 41}, {"ruleId": "705", "severity": 1, "message": "706", "line": 2241, "column": 42, "nodeType": "707", "messageId": "708", "endLine": 2241, "endColumn": 44}, {"ruleId": "711", "severity": 1, "message": "712", "line": 2390, "column": 13, "nodeType": "696", "endLine": 2390, "endColumn": 41}, {"ruleId": "711", "severity": 1, "message": "712", "line": 2391, "column": 32, "nodeType": "696", "endLine": 2391, "endColumn": 60}, {"ruleId": "690", "severity": 1, "message": "732", "line": 35, "column": 5, "nodeType": "692", "endLine": 35, "endColumn": 7, "suggestions": "733"}, {"ruleId": "690", "severity": 1, "message": "734", "line": 55, "column": 6, "nodeType": "692", "endLine": 55, "endColumn": 16, "suggestions": "735"}, {"ruleId": "690", "severity": 1, "message": "736", "line": 94, "column": 6, "nodeType": "692", "endLine": 94, "endColumn": 8, "suggestions": "737"}, {"ruleId": "690", "severity": 1, "message": "738", "line": 107, "column": 6, "nodeType": "692", "endLine": 107, "endColumn": 26, "suggestions": "739"}, {"ruleId": "740", "severity": 1, "message": "741", "line": 190, "column": 19, "nodeType": "696", "endLine": 190, "endColumn": 40}, {"ruleId": "686", "severity": 1, "message": "742", "line": 6, "column": 10, "nodeType": "688", "messageId": "689", "endLine": 6, "endColumn": 21}, {"ruleId": "690", "severity": 1, "message": "743", "line": 35, "column": 6, "nodeType": "692", "endLine": 35, "endColumn": 31, "suggestions": "744"}, {"ruleId": "705", "severity": 1, "message": "709", "line": 650, "column": 38, "nodeType": "707", "messageId": "708", "endLine": 650, "endColumn": 40}, {"ruleId": "686", "severity": 1, "message": "745", "line": 667, "column": 10, "nodeType": "688", "messageId": "689", "endLine": 667, "endColumn": 29}, {"ruleId": "705", "severity": 1, "message": "706", "line": 907, "column": 18, "nodeType": "707", "messageId": "708", "endLine": 907, "endColumn": 20}, {"ruleId": "686", "severity": 1, "message": "746", "line": 944, "column": 9, "nodeType": "688", "messageId": "689", "endLine": 944, "endColumn": 19}, {"ruleId": "705", "severity": 1, "message": "709", "line": 1366, "column": 46, "nodeType": "707", "messageId": "708", "endLine": 1366, "endColumn": 48}, {"ruleId": "686", "severity": 1, "message": "747", "line": 22, "column": 9, "nodeType": "688", "messageId": "689", "endLine": 22, "endColumn": 13}, {"ruleId": "711", "severity": 1, "message": "712", "line": 249, "column": 29, "nodeType": "696", "endLine": 253, "endColumn": 30}, {"ruleId": "711", "severity": 1, "message": "712", "line": 258, "column": 29, "nodeType": "696", "endLine": 266, "endColumn": 30}, {"ruleId": "686", "severity": 1, "message": "748", "line": 1, "column": 17, "nodeType": "688", "messageId": "689", "endLine": 1, "endColumn": 26}, {"ruleId": "686", "severity": 1, "message": "749", "line": 10, "column": 8, "nodeType": "688", "messageId": "689", "endLine": 10, "endColumn": 17}, {"ruleId": "686", "severity": 1, "message": "750", "line": 35, "column": 10, "nodeType": "688", "messageId": "689", "endLine": 35, "endColumn": 20}, {"ruleId": "690", "severity": 1, "message": "751", "line": 83, "column": 6, "nodeType": "692", "endLine": 83, "endColumn": 8, "suggestions": "752"}, {"ruleId": "690", "severity": 1, "message": "753", "line": 87, "column": 6, "nodeType": "692", "endLine": 87, "endColumn": 20, "suggestions": "754"}, {"ruleId": "705", "severity": 1, "message": "709", "line": 121, "column": 29, "nodeType": "707", "messageId": "708", "endLine": 121, "endColumn": 31}, {"ruleId": "690", "severity": 1, "message": "751", "line": 144, "column": 6, "nodeType": "692", "endLine": 144, "endColumn": 44, "suggestions": "755"}, {"ruleId": "705", "severity": 1, "message": "709", "line": 429, "column": 46, "nodeType": "707", "messageId": "708", "endLine": 429, "endColumn": 48}, {"ruleId": "705", "severity": 1, "message": "709", "line": 442, "column": 38, "nodeType": "707", "messageId": "708", "endLine": 442, "endColumn": 40}, {"ruleId": "686", "severity": 1, "message": "756", "line": 4, "column": 29, "nodeType": "688", "messageId": "689", "endLine": 4, "endColumn": 40}, {"ruleId": "711", "severity": 1, "message": "712", "line": 215, "column": 17, "nodeType": "696", "endLine": 219, "endColumn": 52}, {"ruleId": "686", "severity": 1, "message": "756", "line": 4, "column": 29, "nodeType": "688", "messageId": "689", "endLine": 4, "endColumn": 40}, {"ruleId": "711", "severity": 1, "message": "712", "line": 245, "column": 17, "nodeType": "696", "endLine": 249, "endColumn": 52}, {"ruleId": "686", "severity": 1, "message": "756", "line": 4, "column": 10, "nodeType": "688", "messageId": "689", "endLine": 4, "endColumn": 21}, {"ruleId": "686", "severity": 1, "message": "757", "line": 10, "column": 8, "nodeType": "688", "messageId": "689", "endLine": 10, "endColumn": 19}, {"ruleId": "686", "severity": 1, "message": "758", "line": 12, "column": 8, "nodeType": "688", "messageId": "689", "endLine": 12, "endColumn": 13}, {"ruleId": "686", "severity": 1, "message": "759", "line": 15, "column": 9, "nodeType": "688", "messageId": "689", "endLine": 15, "endColumn": 17}, {"ruleId": "686", "severity": 1, "message": "760", "line": 63, "column": 9, "nodeType": "688", "messageId": "689", "endLine": 63, "endColumn": 33}, {"ruleId": "686", "severity": 1, "message": "756", "line": 3, "column": 10, "nodeType": "688", "messageId": "689", "endLine": 3, "endColumn": 21}, {"ruleId": "686", "severity": 1, "message": "758", "line": 10, "column": 8, "nodeType": "688", "messageId": "689", "endLine": 10, "endColumn": 13}, {"ruleId": "686", "severity": 1, "message": "761", "line": 22, "column": 10, "nodeType": "688", "messageId": "689", "endLine": 22, "endColumn": 19}, {"ruleId": "686", "severity": 1, "message": "762", "line": 27, "column": 17, "nodeType": "688", "messageId": "689", "endLine": 27, "endColumn": 25}, {"ruleId": "711", "severity": 1, "message": "763", "line": 203, "column": 17, "nodeType": "696", "endLine": 203, "endColumn": 89}, {"ruleId": "686", "severity": 1, "message": "756", "line": 4, "column": 29, "nodeType": "688", "messageId": "689", "endLine": 4, "endColumn": 40}, {"ruleId": "686", "severity": 1, "message": "720", "line": 6, "column": 10, "nodeType": "688", "messageId": "689", "endLine": 6, "endColumn": 15}, {"ruleId": "686", "severity": 1, "message": "758", "line": 9, "column": 8, "nodeType": "688", "messageId": "689", "endLine": 9, "endColumn": 13}, {"ruleId": "686", "severity": 1, "message": "748", "line": 1, "column": 17, "nodeType": "688", "messageId": "689", "endLine": 1, "endColumn": 26}, {"ruleId": "686", "severity": 1, "message": "764", "line": 1, "column": 28, "nodeType": "688", "messageId": "689", "endLine": 1, "endColumn": 36}, {"ruleId": "686", "severity": 1, "message": "742", "line": 25, "column": 10, "nodeType": "688", "messageId": "689", "endLine": 25, "endColumn": 21}, {"ruleId": "765", "severity": 1, "message": "766", "line": 71, "column": 21, "nodeType": "696", "endLine": 83, "endColumn": 23}, {"ruleId": "705", "severity": 1, "message": "709", "line": 96, "column": 39, "nodeType": "707", "messageId": "708", "endLine": 96, "endColumn": 41}, {"ruleId": "705", "severity": 1, "message": "709", "line": 114, "column": 26, "nodeType": "707", "messageId": "708", "endLine": 114, "endColumn": 28}, {"ruleId": "705", "severity": 1, "message": "709", "line": 115, "column": 26, "nodeType": "707", "messageId": "708", "endLine": 115, "endColumn": 28}, {"ruleId": "705", "severity": 1, "message": "709", "line": 117, "column": 26, "nodeType": "707", "messageId": "708", "endLine": 117, "endColumn": 28}, {"ruleId": "705", "severity": 1, "message": "709", "line": 118, "column": 26, "nodeType": "707", "messageId": "708", "endLine": 118, "endColumn": 28}, {"ruleId": "705", "severity": 1, "message": "709", "line": 119, "column": 26, "nodeType": "707", "messageId": "708", "endLine": 119, "endColumn": 28}, {"ruleId": "705", "severity": 1, "message": "709", "line": 120, "column": 26, "nodeType": "707", "messageId": "708", "endLine": 120, "endColumn": 28}, {"ruleId": "705", "severity": 1, "message": "709", "line": 121, "column": 26, "nodeType": "707", "messageId": "708", "endLine": 121, "endColumn": 28}, {"ruleId": "705", "severity": 1, "message": "709", "line": 122, "column": 26, "nodeType": "707", "messageId": "708", "endLine": 122, "endColumn": 28}, {"ruleId": "686", "severity": 1, "message": "767", "line": 39, "column": 10, "nodeType": "688", "messageId": "689", "endLine": 39, "endColumn": 26}, {"ruleId": "686", "severity": 1, "message": "768", "line": 39, "column": 28, "nodeType": "688", "messageId": "689", "endLine": 39, "endColumn": 47}, {"ruleId": "690", "severity": 1, "message": "769", "line": 235, "column": 6, "nodeType": "692", "endLine": 235, "endColumn": 21, "suggestions": "770"}, {"ruleId": "690", "severity": 1, "message": "771", "line": 277, "column": 6, "nodeType": "692", "endLine": 277, "endColumn": 49, "suggestions": "772"}, {"ruleId": "686", "severity": 1, "message": "773", "line": 19, "column": 3, "nodeType": "688", "messageId": "689", "endLine": 19, "endColumn": 10}, {"ruleId": "686", "severity": 1, "message": "774", "line": 20, "column": 3, "nodeType": "688", "messageId": "689", "endLine": 20, "endColumn": 13}, {"ruleId": "686", "severity": 1, "message": "775", "line": 52, "column": 10, "nodeType": "688", "messageId": "689", "endLine": 52, "endColumn": 20}, {"ruleId": "686", "severity": 1, "message": "776", "line": 59, "column": 15, "nodeType": "688", "messageId": "689", "endLine": 59, "endColumn": 21}, {"ruleId": "686", "severity": 1, "message": "777", "line": 60, "column": 15, "nodeType": "688", "messageId": "689", "endLine": 60, "endColumn": 25}, {"ruleId": "690", "severity": 1, "message": "769", "line": 75, "column": 6, "nodeType": "692", "endLine": 75, "endColumn": 10, "suggestions": "778"}, {"ruleId": "690", "severity": 1, "message": "779", "line": 84, "column": 6, "nodeType": "692", "endLine": 84, "endColumn": 25, "suggestions": "780"}, {"ruleId": "686", "severity": 1, "message": "781", "line": 78, "column": 27, "nodeType": "688", "messageId": "689", "endLine": 78, "endColumn": 45}, {"ruleId": "686", "severity": 1, "message": "782", "line": 1, "column": 14, "nodeType": "688", "messageId": "689", "endLine": 1, "endColumn": 18}, {"ruleId": "686", "severity": 1, "message": "783", "line": 1, "column": 20, "nodeType": "688", "messageId": "689", "endLine": 1, "endColumn": 24}, {"ruleId": "686", "severity": 1, "message": "784", "line": 1, "column": 26, "nodeType": "688", "messageId": "689", "endLine": 1, "endColumn": 29}, {"ruleId": "686", "severity": 1, "message": "785", "line": 1, "column": 31, "nodeType": "688", "messageId": "689", "endLine": 1, "endColumn": 40}, {"ruleId": "686", "severity": 1, "message": "757", "line": 2, "column": 8, "nodeType": "688", "messageId": "689", "endLine": 2, "endColumn": 19}, {"ruleId": "686", "severity": 1, "message": "749", "line": 3, "column": 8, "nodeType": "688", "messageId": "689", "endLine": 3, "endColumn": 17}, {"ruleId": "686", "severity": 1, "message": "786", "line": 8, "column": 40, "nodeType": "688", "messageId": "689", "endLine": 8, "endColumn": 48}, {"ruleId": "686", "severity": 1, "message": "787", "line": 8, "column": 50, "nodeType": "688", "messageId": "689", "endLine": 8, "endColumn": 57}, {"ruleId": "686", "severity": 1, "message": "787", "line": 72, "column": 44, "nodeType": "688", "messageId": "689", "endLine": 72, "endColumn": 51}, {"ruleId": "686", "severity": 1, "message": "788", "line": 17, "column": 66, "nodeType": "688", "messageId": "689", "endLine": 17, "endColumn": 72}, {"ruleId": "686", "severity": 1, "message": "789", "line": 34, "column": 10, "nodeType": "688", "messageId": "689", "endLine": 34, "endColumn": 16}, {"ruleId": "686", "severity": 1, "message": "790", "line": 34, "column": 18, "nodeType": "688", "messageId": "689", "endLine": 34, "endColumn": 27}, {"ruleId": "705", "severity": 1, "message": "706", "line": 256, "column": 32, "nodeType": "707", "messageId": "708", "endLine": 256, "endColumn": 34}, {"ruleId": "705", "severity": 1, "message": "706", "line": 268, "column": 32, "nodeType": "707", "messageId": "708", "endLine": 268, "endColumn": 34}, {"ruleId": "705", "severity": 1, "message": "706", "line": 280, "column": 32, "nodeType": "707", "messageId": "708", "endLine": 280, "endColumn": 34}, {"ruleId": "705", "severity": 1, "message": "706", "line": 292, "column": 32, "nodeType": "707", "messageId": "708", "endLine": 292, "endColumn": 34}, {"ruleId": "705", "severity": 1, "message": "706", "line": 304, "column": 32, "nodeType": "707", "messageId": "708", "endLine": 304, "endColumn": 34}, {"ruleId": "705", "severity": 1, "message": "706", "line": 316, "column": 32, "nodeType": "707", "messageId": "708", "endLine": 316, "endColumn": 34}, {"ruleId": "711", "severity": 1, "message": "712", "line": 479, "column": 21, "nodeType": "696", "endLine": 487, "endColumn": 22}, {"ruleId": "705", "severity": 1, "message": "706", "line": 492, "column": 42, "nodeType": "707", "messageId": "708", "endLine": 492, "endColumn": 44}, {"ruleId": "705", "severity": 1, "message": "706", "line": 492, "column": 68, "nodeType": "707", "messageId": "708", "endLine": 492, "endColumn": 70}, {"ruleId": "711", "severity": 1, "message": "763", "line": 32, "column": 15, "nodeType": "696", "endLine": 52, "endColumn": 16}, {"ruleId": "705", "severity": 1, "message": "706", "line": 35, "column": 33, "nodeType": "707", "messageId": "708", "endLine": 35, "endColumn": 35}, {"ruleId": "711", "severity": 1, "message": "763", "line": 55, "column": 15, "nodeType": "696", "endLine": 55, "endColumn": 27}, {"ruleId": "690", "severity": 1, "message": "791", "line": 47, "column": 6, "nodeType": "692", "endLine": 47, "endColumn": 8, "suggestions": "792"}, {"ruleId": "686", "severity": 1, "message": "793", "line": 69, "column": 7, "nodeType": "688", "messageId": "689", "endLine": 69, "endColumn": 29}, {"ruleId": "686", "severity": 1, "message": "794", "line": 11, "column": 10, "nodeType": "688", "messageId": "689", "endLine": 11, "endColumn": 27}, {"ruleId": "686", "severity": 1, "message": "795", "line": 11, "column": 29, "nodeType": "688", "messageId": "689", "endLine": 11, "endColumn": 49}, {"ruleId": "690", "severity": 1, "message": "796", "line": 20, "column": 6, "nodeType": "692", "endLine": 20, "endColumn": 24, "suggestions": "797"}, {"ruleId": "686", "severity": 1, "message": "798", "line": 153, "column": 15, "nodeType": "688", "messageId": "689", "endLine": 153, "endColumn": 24}, {"ruleId": "686", "severity": 1, "message": "748", "line": 2, "column": 17, "nodeType": "688", "messageId": "689", "endLine": 2, "endColumn": 26}, {"ruleId": "705", "severity": 1, "message": "706", "line": 67, "column": 31, "nodeType": "707", "messageId": "708", "endLine": 67, "endColumn": 33}, {"ruleId": "705", "severity": 1, "message": "706", "line": 141, "column": 33, "nodeType": "707", "messageId": "708", "endLine": 141, "endColumn": 35}, {"ruleId": "690", "severity": 1, "message": "799", "line": 93, "column": 6, "nodeType": "692", "endLine": 93, "endColumn": 40, "suggestions": "800"}, {"ruleId": "690", "severity": 1, "message": "801", "line": 98, "column": 6, "nodeType": "692", "endLine": 98, "endColumn": 40, "suggestions": "802"}, {"ruleId": "690", "severity": 1, "message": "803", "line": 184, "column": 6, "nodeType": "692", "endLine": 184, "endColumn": 41, "suggestions": "804"}, {"ruleId": "686", "severity": 1, "message": "805", "line": 372, "column": 9, "nodeType": "688", "messageId": "689", "endLine": 372, "endColumn": 19}, {"ruleId": "690", "severity": 1, "message": "806", "line": 101, "column": 6, "nodeType": "692", "endLine": 101, "endColumn": 12, "suggestions": "807"}, {"ruleId": "690", "severity": 1, "message": "808", "line": 136, "column": 6, "nodeType": "692", "endLine": 136, "endColumn": 46, "suggestions": "809"}, {"ruleId": "705", "severity": 1, "message": "709", "line": 48, "column": 18, "nodeType": "707", "messageId": "708", "endLine": 48, "endColumn": 20}, {"ruleId": "686", "severity": 1, "message": "810", "line": 52, "column": 24, "nodeType": "688", "messageId": "689", "endLine": 52, "endColumn": 39}, {"ruleId": "690", "severity": 1, "message": "811", "line": 126, "column": 6, "nodeType": "692", "endLine": 126, "endColumn": 16, "suggestions": "812"}, {"ruleId": "690", "severity": 1, "message": "813", "line": 214, "column": 6, "nodeType": "692", "endLine": 214, "endColumn": 46, "suggestions": "814"}, {"ruleId": "690", "severity": 1, "message": "815", "line": 263, "column": 6, "nodeType": "692", "endLine": 263, "endColumn": 32, "suggestions": "816"}, {"ruleId": "817", "severity": 1, "message": "818", "line": 304, "column": 12, "nodeType": "688", "messageId": "819", "endLine": 304, "endColumn": 25}, {"ruleId": "686", "severity": 1, "message": "820", "line": 330, "column": 9, "nodeType": "688", "messageId": "689", "endLine": 330, "endColumn": 30}, {"ruleId": "705", "severity": 1, "message": "709", "line": 572, "column": 37, "nodeType": "707", "messageId": "708", "endLine": 572, "endColumn": 39}, {"ruleId": "686", "severity": 1, "message": "821", "line": 2, "column": 52, "nodeType": "688", "messageId": "689", "endLine": 2, "endColumn": 57}, {"ruleId": "686", "severity": 1, "message": "822", "line": 20, "column": 9, "nodeType": "688", "messageId": "689", "endLine": 20, "endColumn": 13}, {"ruleId": "690", "severity": 1, "message": "823", "line": 67, "column": 6, "nodeType": "692", "endLine": 67, "endColumn": 30, "suggestions": "824"}, {"ruleId": "705", "severity": 1, "message": "709", "line": 353, "column": 36, "nodeType": "707", "messageId": "708", "endLine": 353, "endColumn": 38}, {"ruleId": "705", "severity": 1, "message": "709", "line": 361, "column": 36, "nodeType": "707", "messageId": "708", "endLine": 361, "endColumn": 38}, {"ruleId": "690", "severity": 1, "message": "825", "line": 21, "column": 9, "nodeType": "826", "endLine": 21, "endColumn": 69}, {"ruleId": "690", "severity": 1, "message": "827", "line": 187, "column": 6, "nodeType": "692", "endLine": 187, "endColumn": 57, "suggestions": "828"}, {"ruleId": "686", "severity": 1, "message": "829", "line": 17, "column": 26, "nodeType": "688", "messageId": "689", "endLine": 17, "endColumn": 27}, {"ruleId": "686", "severity": 1, "message": "830", "line": 17, "column": 29, "nodeType": "688", "messageId": "689", "endLine": 17, "endColumn": 35}, {"ruleId": "686", "severity": 1, "message": "831", "line": 17, "column": 37, "nodeType": "688", "messageId": "689", "endLine": 17, "endColumn": 42}, {"ruleId": "690", "severity": 1, "message": "832", "line": 36, "column": 6, "nodeType": "692", "endLine": 36, "endColumn": 16, "suggestions": "833"}, {"ruleId": "705", "severity": 1, "message": "709", "line": 226, "column": 33, "nodeType": "707", "messageId": "708", "endLine": 226, "endColumn": 35}, {"ruleId": "686", "severity": 1, "message": "834", "line": 2, "column": 3, "nodeType": "688", "messageId": "689", "endLine": 2, "endColumn": 12}, {"ruleId": "686", "severity": 1, "message": "714", "line": 8, "column": 3, "nodeType": "688", "messageId": "689", "endLine": 8, "endColumn": 13}, {"ruleId": "705", "severity": 1, "message": "706", "line": 12, "column": 33, "nodeType": "707", "messageId": "708", "endLine": 12, "endColumn": 35}, {"ruleId": "705", "severity": 1, "message": "706", "line": 15, "column": 37, "nodeType": "707", "messageId": "708", "endLine": 15, "endColumn": 39}, {"ruleId": "705", "severity": 1, "message": "706", "line": 18, "column": 33, "nodeType": "707", "messageId": "708", "endLine": 18, "endColumn": 35}, {"ruleId": "835", "severity": 1, "message": "836", "line": 10, "column": 3, "nodeType": "837", "messageId": "708", "endLine": 10, "endColumn": 17}, {"ruleId": "835", "severity": 1, "message": "838", "line": 11, "column": 3, "nodeType": "837", "messageId": "708", "endLine": 11, "endColumn": 18}, {"ruleId": "835", "severity": 1, "message": "839", "line": 48, "column": 3, "nodeType": "837", "messageId": "708", "endLine": 48, "endColumn": 28}, {"ruleId": "840", "severity": 1, "message": "841", "line": 37, "column": 1, "nodeType": "842", "endLine": 108, "endColumn": 3}, {"ruleId": "686", "severity": 1, "message": "843", "line": 4, "column": 10, "nodeType": "688", "messageId": "689", "endLine": 4, "endColumn": 14}, {"ruleId": "686", "severity": 1, "message": "844", "line": 4, "column": 23, "nodeType": "688", "messageId": "689", "endLine": 4, "endColumn": 26}, {"ruleId": "686", "severity": 1, "message": "845", "line": 4, "column": 28, "nodeType": "688", "messageId": "689", "endLine": 4, "endColumn": 31}, {"ruleId": "690", "severity": 1, "message": "846", "line": 165, "column": 6, "nodeType": "692", "endLine": 165, "endColumn": 8, "suggestions": "847"}, "no-unused-vars", "'Utils' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.", "ArrayExpression", ["848"], "react/jsx-pascal-case", "Imported JSX component Home_detail must be in PascalCase or SCREAMING_SNAKE_CASE", "JSXOpeningElement", "usePascalOrSnakeCase", "'image4' is defined but never used.", "'image5' is defined but never used.", "'image6' is defined but never used.", "'RoomActions' is defined but never used.", "'hotels' is assigned a value but never used.", ["849"], "'errors' is assigned a value but never used.", "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "unexpected", "Expected '===' and instead saw '=='.", "'CustomerReviews' is defined but never used.", "jsx-a11y/anchor-is-valid", "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "'use' is defined but never used.", "'InputGroup' is defined but never used.", "'Routers' is defined but never used.", "'error' is assigned a value but never used.", "'setError' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'API_BASE_URL'. Either include it or remove the dependency array.", ["850"], "'Route' is defined but never used.", "'isSearching' is assigned a value but never used.", "'setIsSearching' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleBackToBooking'. Either include it or remove the dependency array.", ["851"], "React Hook useEffect has a missing dependency: 'searchParams'. Either include it or remove the dependency array.", ["852"], "'status' is assigned a value but never used.", "array-callback-return", "Array.prototype.map() expects a value to be returned at the end of arrow function.", "ArrowFunctionExpression", "expectedAtEnd", "React Hook useEffect has a missing dependency: 'fetchReservation'. Either include it or remove the dependency array.", ["853"], "React Hook useEffect has missing dependencies: 'createdAt', 'idReservation', 'messageError', 'messageSuccess', 'navigate', and 'totalPrice'. Either include them or remove the dependency array.", ["854"], "React Hook useEffect has a missing dependency: 'handleAccept'. Either include it or remove the dependency array.", ["855"], "React Hook useEffect has a missing dependency: 'handleDelete'. Either include it or remove the dependency array.", ["856"], "jsx-a11y/heading-has-content", "Headings must have content and the content must be accessible by a screen reader.", "'useLocation' is defined but never used.", "React Hook useEffect has a missing dependency: 'handleConfirm'. Either include it or remove the dependency array.", ["857"], "'currentServiceIndex' is assigned a value but never used.", "'totalPrice' is assigned a value but never used.", "'User' is assigned a value but never used.", "'useEffect' is defined but never used.", "'Factories' is defined but never used.", "'isReadLast' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAllUser'. Either include it or remove the dependency array.", ["858"], "React Hook useEffect has a missing dependency: 'fetchHistoryChat'. Either include it or remove the dependency array.", ["859"], ["860"], "'FaArrowLeft' is defined but never used.", "'AuthActions' is defined but never used.", "'axios' is defined but never used.", "'dispatch' is assigned a value but never used.", "'togglePasswordVisibility' is assigned a value but never used.", "'isLoading' is assigned a value but never used.", "'setEmail' is assigned a value but never used.", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "'useState' is defined but never used.", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "'userReservations' is assigned a value but never used.", "'setUserReservations' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchReservationDetail'. Either include it or remove the dependency array.", ["861"], "React Hook useEffect has missing dependencies: 'dispatch' and 'fetchHotelDetails'. Either include them or remove the dependency array.", ["862"], "'FaPhone' is defined but never used.", "'FaEnvelope' is defined but never used.", "'fontLoaded' is assigned a value but never used.", "'pdfLib' is assigned a value but never used.", "'pdfFontLib' is assigned a value but never used.", ["863"], "React Hook useEffect has a missing dependency: 'fetchHotelDetails'. Either include it or remove the dependency array.", ["864"], "'setSearchParamsObj' is assigned a value but never used.", "'call' is defined but never used.", "'fork' is defined but never used.", "'put' is defined but never used.", "'takeEvery' is defined but never used.", "'onFailed' is assigned a value but never used.", "'onError' is assigned a value but never used.", "'FaUser' is defined but never used.", "'isOpen' is assigned a value but never used.", "'setIsOpen' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'addressMap'. Either include it or remove the dependency array.", ["865"], "'filteredMockPromotions' is assigned a value but never used.", "'selectedPromotion' is assigned a value but never used.", "'setSelectedPromotion' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchPromotions'. Either include it or remove the dependency array.", ["866"], "'startDate' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'activePage', 'sortOption', and 'starFilter'. Either include them or remove the dependency array.", ["867"], "React Hook useEffect has a missing dependency: 'fetchUserFeedbacks'. Either include it or remove the dependency array.", ["868"], "React Hook useEffect has missing dependencies: 'activePage', 'getFilteredFeedbacks', and 'updateURL'. Either include them or remove the dependency array.", ["869"], "'formatDate' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'activePage'. Either include it or remove the dependency array.", ["870"], "React Hook useEffect has missing dependencies: 'activePage' and 'updateURL'. Either include them or remove the dependency array.", ["871"], "'setItemsPerPage' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUserReservations'. Either include it or remove the dependency array.", ["872"], "React Hook useEffect has a missing dependency: 'filters'. Either include it or remove the dependency array.", ["873"], "React Hook useEffect has missing dependencies: 'activePage' and 'totalPages'. Either include them or remove the dependency array. You can also replace multiple useState variables with useReducer if 'setActivePage' needs the current value of 'totalPages'.", ["874"], "no-redeclare", "'parseCurrency' is already defined.", "redeclared", "'calculateRefundPolicy' is assigned a value but never used.", "'Toast' is defined but never used.", "'Auth' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["875"], "The 'safePromotions' conditional could make the dependencies of useEffect Hook (at line 223) change on every render. To fix this, wrap the initialization of 'safePromotions' in its own useMemo() Hook.", "VariableDeclarator", "React Hook useCallback has a missing dependency: 'getPromotionStatus'. Either include it or remove the dependency array.", ["876"], "'X' is defined but never used.", "'Pencil' is defined but never used.", "'Trash' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchUserReports'. Either include it or remove the dependency array.", ["877"], "'Container' is defined but never used.", "no-dupe-keys", "Duplicate key 'UPDATE_PROFILE'.", "ObjectExpression", "Duplicate key 'CHANGE_PASSWORD'.", "Duplicate key 'FETCH_FEEDBACK_BY_HOTELID'.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'Form' is defined but never used.", "'Row' is defined but never used.", "'Col' is defined but never used.", "React Hook useEffect has missing dependencies: 'calculateRefundPolicy' and 'setRefundAmount'. Either include them or remove the dependency array. If 'setRefundAmount' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["878"], {"desc": "879", "fix": "880"}, {"desc": "881", "fix": "882"}, {"desc": "883", "fix": "884"}, {"desc": "885", "fix": "886"}, {"desc": "887", "fix": "888"}, {"desc": "889", "fix": "890"}, {"desc": "891", "fix": "892"}, {"desc": "893", "fix": "894"}, {"desc": "895", "fix": "896"}, {"desc": "897", "fix": "898"}, {"desc": "899", "fix": "900"}, {"desc": "901", "fix": "902"}, {"desc": "903", "fix": "904"}, {"desc": "905", "fix": "906"}, {"desc": "907", "fix": "908"}, {"desc": "909", "fix": "910"}, {"desc": "911", "fix": "912"}, {"desc": "913", "fix": "914"}, {"desc": "915", "fix": "916"}, {"desc": "917", "fix": "918"}, {"desc": "919", "fix": "920"}, {"desc": "921", "fix": "922"}, {"desc": "923", "fix": "924"}, {"desc": "925", "fix": "926"}, {"desc": "927", "fix": "928"}, {"desc": "929", "fix": "930"}, {"desc": "931", "fix": "932"}, {"desc": "933", "fix": "934"}, {"desc": "935", "fix": "936"}, {"desc": "937", "fix": "938"}, {"desc": "939", "fix": "940"}, "Update the dependencies array to be: [Auth?._id, dispatch]", {"range": "941", "text": "942"}, "Update the dependencies array to be: [dispatch]", {"range": "943", "text": "944"}, "Update the dependencies array to be: [dataRestored, subtotal, promotionCode, promotionId, promotionDiscount, API_BASE_URL]", {"range": "945", "text": "946"}, "Update the dependencies array to be: [handleBackToBooking]", {"range": "947", "text": "948"}, "Update the dependencies array to be: [hotelId, dispatch, searchParams]", {"range": "949", "text": "950"}, "Update the dependencies array to be: [fetchReservation]", {"range": "951", "text": "952"}, "Update the dependencies array to be: [createdAt, idReservation, location, messageError, messageSuccess, navigate, totalPrice]", {"range": "953", "text": "954"}, "Update the dependencies array to be: [handleAccept]", {"range": "955", "text": "956"}, "Update the dependencies array to be: [timeLeft, navigate, handleDelete]", {"range": "957", "text": "958"}, "Update the dependencies array to be: [reservationId, navigate, handleConfirm]", {"range": "959", "text": "960"}, "Update the dependencies array to be: [fetchAllUser]", {"range": "961", "text": "962"}, "Update the dependencies array to be: [fetchHistoryChat, selectedUser]", {"range": "963", "text": "964"}, "Update the dependencies array to be: [Socket, Auth._id, selectedUser?._id, fetchAllUser]", {"range": "965", "text": "966"}, "Update the dependencies array to be: [fetchReservationDetail, reservationId]", {"range": "967", "text": "968"}, "Update the dependencies array to be: [Auth, reservationId, hotelId, hotelDetail, dispatch, fetchHotelDetails]", {"range": "969", "text": "970"}, "Update the dependencies array to be: [fetchReservationDetail, id]", {"range": "971", "text": "972"}, "Update the dependencies array to be: [fetchHotelDetails, reservationDetail]", {"range": "973", "text": "974"}, "Update the dependencies array to be: [addressMap]", {"range": "975", "text": "976"}, "Update the dependencies array to be: [fetchPromotions, show, totalPrice]", {"range": "977", "text": "978"}, "Update the dependencies array to be: [activePage, pageParam, sortOption, sortParam, starFilter, starsParam]", {"range": "979", "text": "980"}, "Update the dependencies array to be: [dispatch, fetchUserFeedbacks, sortOption, starFilter]", {"range": "981", "text": "982"}, "Update the dependencies array to be: [feedbacks, starFilter, sortOption, getFilteredFeedbacks, activePage, updateURL]", {"range": "983", "text": "984"}, "Update the dependencies array to be: [activePage, page]", {"range": "985", "text": "986"}, "Update the dependencies array to be: [dispatch, Auth?.favorites, paramsQuery, activePage, updateURL]", {"range": "987", "text": "988"}, "Update the dependencies array to be: [dispatch, fetchUserReservations]", {"range": "989", "text": "990"}, "Update the dependencies array to be: [activeFilter, reservations, dateFilter, filters]", {"range": "991", "text": "992"}, "Update the dependencies array to be: [activePage, filterBill, itemsPerPage, totalPages]", {"range": "993", "text": "994"}, "Update the dependencies array to be: [dispatch, activeStatus, fetchData]", {"range": "995", "text": "996"}, "Update the dependencies array to be: [safePromotions, filters.status, filters.discountType, filters.searchCode, filters.sortOption, activePage, getPromotionStatus]", {"range": "997", "text": "998"}, "Update the dependencies array to be: [Auth._id, fetchUserReports]", {"range": "999", "text": "1000"}, "Update the dependencies array to be: [calculateRefundPolicy, setRefundAmount]", {"range": "1001", "text": "1002"}, [2163, 2174], "[Auth?._id, dispatch]", [3226, 3228], "[dispatch]", [9969, 10040], "[dataRestored, subtotal, promotionCode, promotionId, promotionDiscount, API_BASE_URL]", [6200, 6202], "[handleBackToBooking]", [9936, 9955], "[hotelId, dispatch, searchParams]", [1405, 1407], "[fetchReservation]", [1841, 1851], "[createdAt, idReservation, location, messageError, messageSuccess, navigate, totalPrice]", [3122, 3124], "[handleAccept]", [3447, 3467], "[timeLeft, navigate, handleDelete]", [1197, 1222], "[reservationId, navigate, handleConfirm]", [2913, 2915], "[fetchAllUser]", [2972, 2986], "[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>]", [4575, 4613], "[Socket, Auth._id, selectedUser?._id, fetchAllUser]", [7500, 7515], "[fetchReservationDetail, reservationId]", [9032, 9075], "[Auth, reservationId, hotelId, hotelDetail, dispatch, fetchHotelDetails]", [2124, 2128], "[fetchReservationDetail, id]", [2328, 2347], "[fetchHotelDetails, reservationDetail]", [1416, 1418], "[addressMap]", [905, 923], "[fetchPromotions, show, totalPrice]", [3136, 3170], "[activePage, pageParam, sortOption, sortParam, starFilter, starsParam]", [3276, 3310], "[dispatch, fetchUserFeedbacks, sortOption, starFilter]", [6005, 6040], "[feedbacks, starFilter, sortOption, getFilteredFeedbacks, activePage, updateURL]", [3659, 3665], "[activePage, page]", [4664, 4704], "[dispatch, Auth?.favorites, paramsQuery, activePage, updateURL]", [4702, 4712], "[dispatch, fetchUserReservations]", [7697, 7737], "[activeFilter, reservations, dateFilter, filters]", [9171, 9197], "[activePage, filterBill, itemsPerPage, totalPages]", [2318, 2342], "[dispatch, activeStatus, fetchData]", [7357, 7408], "[safePromotions, filters.status, filters.discountType, filters.searchCode, filters.sortOption, activePage, getPromotionStatus]", [1363, 1373], "[Auth._id, fetchUserReports]", [5035, 5037], "[calculateRefundPolicy, setRefundAmount]"]